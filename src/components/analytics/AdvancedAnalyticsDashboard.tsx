
import { useState } from 'react';
import { Users, TrendingUp } from 'lucide-react';
import CustomerSegmentation from './CustomerSegmentation';
import ProfitMarginAnalysis from './ProfitMarginAnalysis';

const AdvancedAnalyticsDashboard = () => {
  const [activeTab, setActiveTab] = useState('segmentation');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-900">Advanced Analytics Dashboard</h2>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm border border-gray-200">
        {[
          { id: 'segmentation', label: 'Customer Segmentation & LTV', icon: Users },
          { id: 'profitability', label: 'Profit Margin Analysis', icon: TrendingUp }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'segmentation' && <CustomerSegmentation />}
      {activeTab === 'profitability' && <ProfitMarginAnalysis />}
    </div>
  );
};

export default AdvancedAnalyticsDashboard;
