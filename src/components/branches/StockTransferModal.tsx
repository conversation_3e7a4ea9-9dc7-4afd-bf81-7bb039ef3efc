
import { useState } from 'react';
import { X, ArrowRightLeft } from 'lucide-react';
import { Branch } from '../../types/branch';
import { createStockTransfer } from '../../services/branchService';

interface StockTransferModalProps {
  branches: Branch[];
  onClose: () => void;
  onTransferCreated: () => void;
}

const StockTransferModal = ({ branches, onClose, onTransferCreated }: StockTransferModalProps) => {
  const [formData, setFormData] = useState({
    fromBranchId: '',
    toBranchId: '',
    productId: 'PRD-001', // Mock product ID
    quantity: '',
    notes: ''
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.fromBranchId || !formData.toBranchId || !formData.quantity) {
      alert('Please fill in all required fields');
      return;
    }

    if (formData.fromBranchId === formData.toBranchId) {
      alert('Source and destination branches must be different');
      return;
    }

    setLoading(true);
    try {
      await createStockTransfer({
        fromBranchId: formData.fromBranchId,
        toBranchId: formData.toBranchId,
        productId: formData.productId,
        quantity: parseInt(formData.quantity),
        status: 'pending',
        requestedBy: 'current-user-id',
        notes: formData.notes || undefined
      });
      
      onTransferCreated();
    } catch (error) {
      console.error('Error creating stock transfer:', error);
      alert('Error creating stock transfer');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <ArrowRightLeft className="h-5 w-5 text-teal-600" />
            <h3 className="text-lg font-semibold text-gray-900">New Stock Transfer</h3>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From Branch *
            </label>
            <select
              value={formData.fromBranchId}
              onChange={(e) => setFormData(prev => ({ ...prev, fromBranchId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              required
            >
              <option value="">Select source branch</option>
              {branches.map(branch => (
                <option key={branch.id} value={branch.id}>{branch.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              To Branch *
            </label>
            <select
              value={formData.toBranchId}
              onChange={(e) => setFormData(prev => ({ ...prev, toBranchId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              required
            >
              <option value="">Select destination branch</option>
              {branches.filter(b => b.id !== formData.fromBranchId).map(branch => (
                <option key={branch.id} value={branch.id}>{branch.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product
            </label>
            <select
              value={formData.productId}
              onChange={(e) => setFormData(prev => ({ ...prev, productId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="PRD-001">Premium Ballpoint Pen Set</option>
              <option value="PRD-002">A4 Notebook Pack</option>
              <option value="PRD-003">Desk Organizer Set</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity *
            </label>
            <input
              type="number"
              min="1"
              value={formData.quantity}
              onChange={(e) => setFormData(prev => ({ ...prev, quantity: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              placeholder="Enter quantity"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none"
              rows={3}
              placeholder="Optional notes about the transfer"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Transfer'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StockTransferModal;
