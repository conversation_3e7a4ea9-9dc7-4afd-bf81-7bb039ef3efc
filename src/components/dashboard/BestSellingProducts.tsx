import { useState, useEffect } from 'react';
import { TrendingUp, Star, ShoppingCart, Package, Eye, ArrowUp, ArrowDown } from 'lucide-react';
import { getBestSellingProducts, BestSellingProduct } from '../../services/analyticsService';
import { realTimeService } from '../../services/realTimeService';
import ProductDetailsModal from '../products/ProductDetailsModal';

interface BestSellingProductsProps {
  userType: 'client' | 'reseller';
  onAddToCart?: (product: any) => void;
  onViewProduct?: (productId: string) => void;
}

const BestSellingProducts = ({ userType, onAddToCart, onViewProduct }: BestSellingProductsProps) => {
  const [products, setProducts] = useState<BestSellingProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [showProductModal, setShowProductModal] = useState(false);

  useEffect(() => {
    loadBestSellingProducts();

    // Subscribe to real-time updates
    const unsubscribeProduct = realTimeService.subscribe('product-updated', handleProductUpdate);
    const unsubscribeStock = realTimeService.subscribe('stock-updated', handleStockUpdate);
    const unsubscribePrice = realTimeService.subscribe('price-updated', handlePriceUpdate);

    return () => {
      unsubscribeProduct();
      unsubscribeStock();
      unsubscribePrice();
    };
  }, []);

  const loadBestSellingProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getBestSellingProducts(4);
      setProducts(data);
    } catch (err) {
      setError('Failed to load best selling products');
      console.error('Error loading best selling products:', err);
    } finally {
      setLoading(false);
    }
  };

  // Real-time event handlers
  const handleProductUpdate = (event: any) => {
    const { productId, newData } = event.data;
    setProducts(prev => prev.map(product =>
      product.id === productId ? { ...product, ...newData } : product
    ));
  };

  const handleStockUpdate = (event: any) => {
    const { productId, newStock } = event.data;
    setProducts(prev => prev.map(product =>
      product.id === productId ? { ...product, stock: newStock, inStock: newStock > 0 } : product
    ));
  };

  const handlePriceUpdate = (event: any) => {
    const { productId, newPrice, newResellerPrice } = event.data;
    setProducts(prev => prev.map(product =>
      product.id === productId ? {
        ...product,
        price: newPrice || product.price,
        resellerPrice: newResellerPrice || product.resellerPrice
      } : product
    ));
  };

  const handleAddToCart = (product: BestSellingProduct) => {
    if (onAddToCart) {
      const cartProduct = {
        id: parseInt(product.id.replace('PRD-', '')),
        title: product.title,
        category: product.category,
        price: product.price,
        resellerPrice: product.resellerPrice,
        image: product.image,
        featuredImage: product.image, // Use the same image for now
        rating: product.rating,
        inStock: product.inStock,
        stock: product.stock
      };
      onAddToCart(cartProduct);
    }
  };

  const handleViewProduct = (productId: string) => {
    setSelectedProductId(productId);
    setShowProductModal(true);

    // Also call the optional callback
    if (onViewProduct) {
      onViewProduct(productId);
    }
  };

  const handleCloseModal = () => {
    setShowProductModal(false);
    setSelectedProductId(null);
  };

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} Dh`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-32 mb-3"></div>
              <div className="bg-gray-200 rounded h-4 mb-2"></div>
              <div className="bg-gray-200 rounded h-3 w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        <div className="text-center py-8">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={loadBestSellingProducts}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-3 sm:space-y-0">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-lg sm:text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-xs sm:text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        {userType === 'reseller' && (
          <div className="bg-gradient-to-r from-teal-50 to-teal-100 px-3 py-1 rounded-full self-start sm:self-auto">
            <span className="text-xs sm:text-sm font-medium text-teal-700">Reseller Pricing</span>
          </div>
        )}
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {products.map((product, index) => {
          const displayPrice = userType === 'reseller' ? product.resellerPrice : product.price;
          const savings = userType === 'reseller' ? product.price - product.resellerPrice : 0;
          
          return (
            <div
              key={product.id}
              className="group relative bg-gradient-to-br from-gray-50 to-white border border-gray-200 rounded-xl p-4 hover:shadow-lg hover:border-orange-200 transition-all duration-300 hover:-translate-y-1"
            >
              {/* Rank Badge */}
              <div className="absolute -top-2 -left-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs font-bold w-6 h-6 rounded-full flex items-center justify-center shadow-lg">
                {index + 1}
              </div>

              {/* Growth Indicator */}
              <div className="absolute top-2 right-2 flex items-center space-x-1">
                {product.growth > 0 ? (
                  <ArrowUp className="h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDown className="h-3 w-3 text-red-500" />
                )}
                <span className={`text-xs font-medium ${product.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(product.growth).toFixed(1)}%
                </span>
              </div>

              {/* Product Image */}
              <div className="bg-gray-100 rounded-lg h-24 mb-3 flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                <Package className="h-8 w-8 text-gray-400" />
              </div>

              {/* Product Info */}
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-900 text-sm line-clamp-2 group-hover:text-orange-600 transition-colors">
                  {product.title}
                </h4>
                
                <div className="flex items-center space-x-1">
                  <Star className="h-3 w-3 text-yellow-400 fill-current" />
                  <span className="text-xs text-gray-600">{product.rating}</span>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-gray-900">
                      {formatPrice(displayPrice)}
                    </span>
                    {userType === 'reseller' && savings > 0 && (
                      <span className="text-xs text-gray-500 line-through">
                        {formatPrice(product.price)}
                      </span>
                    )}
                  </div>
                  
                  {userType === 'reseller' && savings > 0 && (
                    <div className="text-xs text-green-600 font-medium">
                      Save {formatPrice(savings)}
                    </div>
                  )}
                </div>

                {/* Sales Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{formatNumber(product.unitsSold)} sold</span>
                  <span className={`px-2 py-1 rounded-full ${product.inStock ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                    {product.inStock ? `${product.stock} left` : 'Out of stock'}
                  </span>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2 pt-2">
                  <button
                    onClick={() => handleViewProduct(product.id)}
                    className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-xs font-medium hover:bg-gray-200 transition-colors flex items-center justify-center space-x-1"
                  >
                    <Eye className="h-3 w-3" />
                    <span>View</span>
                  </button>
                  
                  {product.inStock && onAddToCart && (
                    <button
                      onClick={() => handleAddToCart(product)}
                      className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-2 rounded-lg text-xs font-medium hover:from-orange-600 hover:to-orange-700 transition-all flex items-center justify-center space-x-1"
                    >
                      <ShoppingCart className="h-3 w-3" />
                      <span>Add</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer Stats */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 text-center">
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-lg sm:text-2xl font-bold text-gray-900">
              {formatNumber(products.reduce((sum, p) => sum + p.unitsSold, 0))}
            </p>
            <p className="text-xs text-gray-600">Total Units Sold</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-lg sm:text-2xl font-bold text-gray-900">
              {formatPrice(products.reduce((sum, p) => sum + p.revenue, 0))}
            </p>
            <p className="text-xs text-gray-600">Total Revenue</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-lg sm:text-2xl font-bold text-green-600">
              +{(products.reduce((sum, p) => sum + p.growth, 0) / products.length).toFixed(1)}%
            </p>
            <p className="text-xs text-gray-600">Avg Growth</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-lg sm:text-2xl font-bold text-gray-900">
              {(products.reduce((sum, p) => sum + p.rating, 0) / products.length).toFixed(1)}
            </p>
            <p className="text-xs text-gray-600">Avg Rating</p>
          </div>
        </div>
      </div>

      {/* Product Details Modal */}
      <ProductDetailsModal
        productId={selectedProductId}
        isOpen={showProductModal}
        onClose={handleCloseModal}
        userType={userType}
        onAddToCart={onAddToCart}
      />
    </div>
  );
};

export default BestSellingProducts;
