import { useState } from 'react';
import NavigationTabs from './components/NavigationTabs';
import TabContent from './components/TabContent';
import ProfileManagement from '../profile/ProfileManagement';
import PageLayout from '../layout/PageLayout';
import UserManagement from '../admin/UserManagement';
import ProductManagement from '../inventory/ProductManagement';
import BranchManagement from '../branches/BranchManagement';
import CategoryManagement from '../inventory/CategoryManagement';
import OrderManagement from '../orders/OrderManagement';
import AnalyticsPage from '../pages/AnalyticsPage';
import SecurityPage from '../pages/SecurityPage';
import SystemHealthPage from '../pages/SystemHealthPage';
import NotificationsPage from '../pages/NotificationsPage';
import InvoiceManagementPage from '../pages/InvoiceManagementPage';
import AuditLogsPage from '../pages/AuditLogsPage';
import SystemSettingsPage from '../pages/SystemSettingsPage';
import ClientManagement from '../clients/ClientManagement';

interface AdminDashboardProps {
  user: any;
}

const AdminDashboard = ({ user: initialUser }: AdminDashboardProps) => {
  const [user, setUser] = useState(initialUser);
  const [activeTab, setActiveTab] = useState('overview');
  const [currentPage, setCurrentPage] = useState('dashboard');

  const handleUserUpdate = (updatedUser: any) => {
    setUser(updatedUser);
  };

  const handleNavigate = (page: string) => {
    setCurrentPage(page);
    if (page === 'dashboard') {
      setActiveTab('overview');
    }
  };

  // Render different pages based on currentPage
  const renderPage = () => {
    switch (currentPage) {
      case 'user-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="User Management"
            subtitle="Manage admin and store manager accounts"
            breadcrumbs={[{ label: 'System Management', page: 'dashboard' }]}
          >
            <UserManagement currentUserId={user.id} />
          </PageLayout>
        );
      
      case 'product-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Product Management"
            subtitle="Manage products, inventory, and pricing"
            breadcrumbs={[{ label: 'Inventory', page: 'dashboard' }]}
          >
            <ProductManagement />
          </PageLayout>
        );
      
      case 'branch-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Branch Management"
            subtitle="Manage store branches and locations"
            breadcrumbs={[{ label: 'Operations', page: 'dashboard' }]}
          >
            <BranchManagement />
          </PageLayout>
        );
      
      case 'category-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Category Management"
            subtitle="Organize products into categories"
            breadcrumbs={[{ label: 'Inventory', page: 'dashboard' }]}
          >
            <CategoryManagement />
          </PageLayout>
        );
      
      case 'order-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Order Management"
            subtitle="Process and track customer orders"
            breadcrumbs={[{ label: 'Sales', page: 'dashboard' }]}
          >
            <OrderManagement />
          </PageLayout>
        );
      
      case 'analytics':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Analytics & Reports"
            subtitle="Business insights and performance metrics"
            breadcrumbs={[{ label: 'Reports', page: 'dashboard' }]}
          >
            <AnalyticsPage />
          </PageLayout>
        );
      
      case 'security':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Security Management"
            subtitle="System security and access control"
            breadcrumbs={[{ label: 'System', page: 'dashboard' }]}
          >
            <SecurityPage />
          </PageLayout>
        );
      
      case 'system-health':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="System Health"
            subtitle="Monitor system performance and status"
            breadcrumbs={[{ label: 'System', page: 'dashboard' }]}
          >
            <SystemHealthPage />
          </PageLayout>
        );
      
      case 'notifications':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Notification Center"
            subtitle="Manage system notifications and alerts"
            breadcrumbs={[{ label: 'Communication', page: 'dashboard' }]}
          >
            <NotificationsPage />
          </PageLayout>
        );
      
      case 'invoices':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Invoice Management"
            subtitle="Generate and manage invoices"
            breadcrumbs={[{ label: 'Finance', page: 'dashboard' }]}
          >
            <InvoiceManagementPage />
          </PageLayout>
        );
      
      case 'audit-logs':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Audit Logs"
            subtitle="System activity and security logs"
            breadcrumbs={[{ label: 'System', page: 'dashboard' }]}
          >
            <AuditLogsPage />
          </PageLayout>
        );
      
      case 'settings':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="System Settings"
            subtitle="Configure system preferences"
            breadcrumbs={[{ label: 'System', page: 'dashboard' }]}
          >
            <SystemSettingsPage />
          </PageLayout>
        );
      
      case 'profile':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Profile Management"
            subtitle="Manage your account settings"
          >
            <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
          </PageLayout>
        );
      
      default:
        // Dashboard view with tabs
        return (
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-6 sm:p-8 text-white">
              <h2 className="text-2xl sm:text-3xl font-bold mb-2">Welcome back, {user.fullName}!</h2>
              <p className="text-base sm:text-lg opacity-90">Admin Dashboard</p>
            </div>
            
            <NavigationTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              userRole="admin"
            />

            {activeTab === 'profile' ? (
              <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
            ) : (
              <TabContent
                activeTab={activeTab}
                userRole="admin"
                user={user}
                onNavigate={handleNavigate}
              />
            )}
          </div>
        );
    }
  };

  return renderPage();
};

export default AdminDashboard;
