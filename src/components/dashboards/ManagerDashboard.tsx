
import { useState, useEffect } from 'react';
import { getLowStockAlerts } from '../../services/inventoryService';
import { LowStockAlert } from '../../types/inventory';
import DashboardHeader from './components/DashboardHeader';
import NavigationTabs from './components/NavigationTabs';
import QuickStats from './components/QuickStats';
import TabContent from './components/TabContent';
import ProfileManagement from '../profile/ProfileManagement';
import PromoCodeManagement from '../promoCode/PromoCodeManagement';
import PageLayout from '../layout/PageLayout';
import ProductManagement from '../inventory/ProductManagement';
import CategoryManagement from '../inventory/CategoryManagement';
import BranchManagement from '../branches/BranchManagement';
import ClientManagement from '../clients/ClientManagement';

interface ManagerDashboardProps {
  user: any;
}

const ManagerDashboard = ({ user: initialUser }: ManagerDashboardProps) => {
  const [user, setUser] = useState(initialUser);
  const [activeTab, setActiveTab] = useState('inventory');
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [lowStockAlerts, setLowStockAlerts] = useState<LowStockAlert[]>([]);

  useEffect(() => {
    loadLowStockAlerts();
  }, []);

  const loadLowStockAlerts = async () => {
    try {
      const alerts = await getLowStockAlerts();
      setLowStockAlerts(alerts);
    } catch (error) {
      console.error('Error loading low stock alerts:', error);
    }
  };

  const handleUserUpdate = (updatedUser: any) => {
    setUser(updatedUser);
  };

  const handleNavigate = (page: string) => {
    setCurrentPage(page);
    if (page === 'dashboard') {
      setActiveTab('inventory');
    }
  };

  const branchStats = {
    totalProducts: 456,
    lowStockItems: lowStockAlerts.length,
    pendingOrders: 23,
    todaysSales: 4250
  };

  // Render different pages based on currentPage
  const renderPage = () => {
    switch (currentPage) {
      case 'product-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Product Management"
            subtitle="Manage products, inventory, and pricing"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <ProductManagement />
          </PageLayout>
        );

      case 'category-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Category Management"
            subtitle="Organize products into categories"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <CategoryManagement />
          </PageLayout>
        );

      case 'branch-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Branch Management"
            subtitle="Manage store branches and locations"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <BranchManagement />
          </PageLayout>
        );

      case 'client-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Client Management"
            subtitle="Manage clients and resellers"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <ClientManagement currentUserId={user.id} userRole="manager" />
          </PageLayout>
        );

      case 'promo-codes':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Promo Code Management"
            subtitle="Create and manage promotional codes"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <PromoCodeManagement />
          </PageLayout>
        );

      case 'profile':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Profile Management"
            subtitle="Manage your account settings"
          >
            <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
          </PageLayout>
        );

      default:
        // Dashboard view with tabs
        return (
          <div className="space-y-6">
            <DashboardHeader userFullName={user.fullName} />

            <NavigationTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              userRole="manager"
            />

            {!['profile', 'promo-codes', 'product-management', 'category-management', 'branch-management'].includes(activeTab) && <QuickStats stats={branchStats} />}

            {activeTab === 'profile' ? (
              <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
            ) : activeTab === 'promo-codes' ? (
              <PromoCodeManagement />
            ) : (
              <TabContent
                activeTab={activeTab}
                lowStockAlerts={lowStockAlerts}
                onRefreshLowStock={loadLowStockAlerts}
                userRole="manager"
                user={user}
                onNavigate={handleNavigate}
              />
            )}
          </div>
        );
    }
  };

  return renderPage();
};

export default ManagerDashboard;
