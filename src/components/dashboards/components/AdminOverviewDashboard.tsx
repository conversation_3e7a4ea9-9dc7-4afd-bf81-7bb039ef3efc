
import { useState, useEffect } from 'react';
import {
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  TrendingUp,
  AlertTriangle,
  BarChart3,
  Download,
  Eye,
  Activity,
  Target,
  Percent,
  Building2,
  Shield,
  Bell
} from 'lucide-react';
import { getSalesReport, getInventoryAnalytics, getCustomerAnalytics, getKPIMetrics } from '../../../services/analyticsService';
import { getProducts } from '../../../services/inventoryService';
import { getOrders } from '../../../services/orderService';
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import ManagementCards from './ManagementCards';

interface AdminOverviewDashboardProps {
  onNavigate?: (page: string) => void;
}

const AdminOverviewDashboard = ({ onNavigate }: AdminOverviewDashboardProps = {}) => {
  const [salesData, setSalesData] = useState<any>(null);
  const [inventoryData, setInventoryData] = useState<any>(null);
  const [customerData, setCustomerData] = useState<any>(null);
  const [kpiData, setKpiData] = useState<any>(null);
  const [period, setPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, [period]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [sales, inventory, customers, kpis] = await Promise.all([
        getSalesReport(period),
        getInventoryAnalytics(),
        getCustomerAnalytics(),
        getKPIMetrics()
      ]);

      setSalesData(sales);
      setInventoryData(inventory);
      setCustomerData(customers);
      setKpiData(kpis);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
    setLoading(false);
  };

  // Management cards configuration
  const managementCards = [
    {
      title: 'User Management',
      description: 'Manage admin and store manager accounts',
      icon: Users,
      color: 'from-blue-500 to-blue-600',
      stats: '156 Users',
      page: 'user-management'
    },
    {
      title: 'Product Management',
      description: 'Manage products, inventory, and pricing',
      icon: Package,
      color: 'from-green-500 to-green-600',
      stats: `${inventoryData?.totalProducts || 0} Products`,
      page: 'product-management'
    },
    {
      title: 'Branch Management',
      description: 'Manage store branches and locations',
      icon: Building2,
      color: 'from-purple-500 to-purple-600',
      stats: '12 Branches',
      page: 'branch-management'
    },
    {
      title: 'Order Management',
      description: 'Process and track customer orders',
      icon: ShoppingCart,
      color: 'from-orange-500 to-orange-600',
      stats: `${salesData?.totalOrders || 0} Orders`,
      page: 'order-management'
    },
    {
      title: 'Analytics & Reports',
      description: 'Business insights and performance metrics',
      icon: BarChart3,
      color: 'from-teal-500 to-teal-600',
      stats: 'Live Data',
      page: 'analytics'
    },
    {
      title: 'Security Management',
      description: 'System security and access control',
      icon: Shield,
      color: 'from-red-500 to-red-600',
      stats: 'Secure',
      page: 'security'
    },
    {
      title: 'System Health',
      description: 'Monitor system performance and status',
      icon: Activity,
      color: 'from-indigo-500 to-indigo-600',
      stats: 'Healthy',
      page: 'system-health'
    },
    {
      title: 'Notifications',
      description: 'Manage system notifications and alerts',
      icon: Bell,
      color: 'from-yellow-500 to-yellow-600',
      stats: '5 New',
      page: 'notifications'
    }
  ];

  const exportData = (type: 'csv' | 'excel') => {
    console.log(`Exporting data as ${type}`);
    // Implementation for export functionality
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Period Selector and Export */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Overview Reports & Analytics</h2>
          <p className="text-gray-600">Complete business intelligence dashboard</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value as any)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
          </select>
          <div className="flex space-x-2">
            <button
              onClick={() => exportData('csv')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              <Download className="h-4 w-4" />
              <span>CSV</span>
            </button>
            <button
              onClick={() => exportData('excel')}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Download className="h-4 w-4" />
              <span>Excel</span>
            </button>
          </div>
        </div>
      </div>

      {/* Management Cards */}
      <ManagementCards cards={managementCards} onNavigate={onNavigate} />

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-xl text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Today's Sales</p>
              <p className="text-2xl font-bold">{salesData?.totalSales?.toLocaleString()} Dh</p>
              <p className="text-blue-200 text-sm">+{kpiData?.revenue?.growth?.toFixed(1)}% vs yesterday</p>
            </div>
            <DollarSign className="h-8 w-8 text-blue-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-xl text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Total Orders</p>
              <p className="text-2xl font-bold">{salesData?.totalOrders}</p>
              <p className="text-green-200 text-sm">+{kpiData?.orders?.growth?.toFixed(1)}% vs last period</p>
            </div>
            <ShoppingCart className="h-8 w-8 text-green-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-xl text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Total Customers</p>
              <p className="text-2xl font-bold">{customerData?.totalCustomers}</p>
              <p className="text-purple-200 text-sm">+{kpiData?.customers?.growth?.toFixed(1)}% growth</p>
            </div>
            <Users className="h-8 w-8 text-purple-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6 rounded-xl text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-100 text-sm">Conversion Rate</p>
              <p className="text-2xl font-bold">{kpiData?.conversionRate?.current?.toFixed(1)}%</p>
              <p className="text-orange-200 text-sm">+{kpiData?.conversionRate?.growth?.toFixed(1)}% improvement</p>
            </div>
            <Percent className="h-8 w-8 text-orange-200" />
          </div>
        </div>
      </div>

      {/* Secondary KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{inventoryData?.totalProducts}</p>
            </div>
            <Package className="h-8 w-8 text-gray-400" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Low Stock Items</p>
              <p className="text-2xl font-bold text-red-600">{inventoryData?.lowStockItems}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Avg Order Value</p>
              <p className="text-2xl font-bold text-gray-900">{kpiData?.averageOrderValue?.current?.toFixed(0)} Dh</p>
            </div>
            <Target className="h-8 w-8 text-gray-400" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Inventory Turnover</p>
              <p className="text-2xl font-bold text-gray-900">{kpiData?.inventoryTurnover?.current?.toFixed(1)}x</p>
            </div>
            <Activity className="h-8 w-8 text-gray-400" />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Trend Chart */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Sales Trend</h3>
            <TrendingUp className="h-5 w-5 text-teal-600" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={salesData?.dailySales}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="sales" stroke="#0d9488" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Category Performance */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Category Performance</h3>
            <BarChart3 className="h-5 w-5 text-teal-600" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={inventoryData?.categoryPerformance}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ category, percentage }) => `${category}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="revenue"
              >
                {inventoryData?.categoryPerformance?.map((entry: any, index: number) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Stock Movements */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Stock Movements</h3>
            <Package className="h-5 w-5 text-teal-600" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={inventoryData?.stockMovements}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="inbound" fill="#10b981" />
              <Bar dataKey="outbound" fill="#ef4444" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Customer Segments */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Customer Segments</h3>
            <Users className="h-5 w-5 text-teal-600" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={customerData?.customerSegments}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="segment" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#8b5cf6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Top Products and Customers Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Selling Products */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Selling Products</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2">Product</th>
                  <th className="text-left py-2">Sales</th>
                  <th className="text-left py-2">Revenue</th>
                </tr>
              </thead>
              <tbody>
                {inventoryData?.topSellingProducts?.map((product: any, index: number) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-2">{product.name}</td>
                    <td className="py-2">{product.unitsSold}</td>
                    <td className="py-2">{product.turnoverRate} Dh</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Top Customers */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Customers</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2">Customer</th>
                  <th className="text-left py-2">Orders</th>
                  <th className="text-left py-2">Total Spent</th>
                </tr>
              </thead>
              <tbody>
                {customerData?.topCustomers?.map((customer: any, index: number) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-2">{customer.name}</td>
                    <td className="py-2">{customer.orderCount}</td>
                    <td className="py-2">{customer.totalSpent} Dh</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminOverviewDashboard;
