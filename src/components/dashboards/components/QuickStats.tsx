
import { Package, AlertTriangle, ShoppingCart, TrendingUp } from 'lucide-react';

interface QuickStatsProps {
  stats: {
    totalProducts: number;
    lowStockItems: number;
    pendingOrders: number;
    todaysSales: number;
  };
}

const QuickStats = ({ stats }: QuickStatsProps) => {
  const statCards = [
    {
      title: 'Total Products',
      value: stats.totalProducts,
      icon: Package,
      bgColor: 'bg-blue-100',
      iconColor: 'text-blue-600',
      textColor: 'text-gray-900'
    },
    {
      title: 'Low Stock Items',
      value: stats.lowStockItems,
      icon: AlertTriangle,
      bgColor: 'bg-red-100',
      iconColor: 'text-red-600',
      textColor: 'text-red-600'
    },
    {
      title: 'Pending Orders',
      value: stats.pendingOrders,
      icon: ShoppingCart,
      bgColor: 'bg-orange-100',
      iconColor: 'text-orange-600',
      textColor: 'text-orange-600'
    },
    {
      title: 'Today\'s Sales',
      value: `${stats.todaysSales} Dh`,
      icon: TrendingUp,
      bgColor: 'bg-green-100',
      iconColor: 'text-green-600',
      textColor: 'text-green-600'
    }
  ];

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
      {statCards.map((card, index) => (
        <div key={index} className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">{card.title}</p>
              <p className={`text-lg sm:text-2xl font-bold ${card.textColor}`}>{card.value}</p>
            </div>
            <div className={`${card.bgColor} p-2 sm:p-3 rounded-lg`}>
              <card.icon className={`h-5 w-5 sm:h-6 sm:w-6 ${card.iconColor}`} />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default QuickStats;
