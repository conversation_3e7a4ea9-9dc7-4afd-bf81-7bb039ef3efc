import { useState } from 'react';
import { Shield, Lock, Eye, AlertTriangle, CheckCircle, Activity, Users, Key } from 'lucide-react';

const SecurityPage = () => {
  const [activeSection, setActiveSection] = useState('overview');

  const securityMetrics = [
    { label: 'Active Sessions', value: '24', status: 'normal', icon: Users },
    { label: 'Failed Login Attempts', value: '3', status: 'warning', icon: AlertTriangle },
    { label: 'Security Alerts', value: '0', status: 'good', icon: Shield },
    { label: 'Last Security Scan', value: '2 hours ago', status: 'good', icon: Activity }
  ];

  const recentSecurityEvents = [
    { event: 'User login from new device', user: '<PERSON>', time: '5 minutes ago', severity: 'info' },
    { event: 'Password changed', user: 'Fatima Alami', time: '1 hour ago', severity: 'info' },
    { event: 'Failed login attempt', user: 'Unknown', time: '2 hours ago', severity: 'warning' },
    { event: 'Admin access granted', user: '<PERSON>', time: '3 hours ago', severity: 'info' }
  ];

  return (
    <div className="space-y-6">
      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {securityMetrics.map((metric, index) => {
          const Icon = metric.icon;
          const statusColors = {
            good: 'bg-green-50 border-green-200 text-green-800',
            warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
            normal: 'bg-blue-50 border-blue-200 text-blue-800'
          };
          
          return (
            <div key={index} className={`rounded-lg p-6 border ${statusColors[metric.status]}`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium opacity-80">{metric.label}</p>
                  <p className="text-2xl font-bold">{metric.value}</p>
                </div>
                <Icon className="h-8 w-8 opacity-60" />
              </div>
            </div>
          );
        })}
      </div>

      {/* Security Sections */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'access-control', label: 'Access Control' },
              { id: 'audit-logs', label: 'Audit Logs' },
              { id: 'settings', label: 'Security Settings' }
            ].map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeSection === section.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {section.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeSection === 'overview' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Security Events</h3>
                <div className="space-y-3">
                  {recentSecurityEvents.map((event, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className={`w-2 h-2 rounded-full ${
                        event.severity === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                      }`}></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{event.event}</p>
                        <p className="text-xs text-gray-500">{event.user} • {event.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeSection === 'access-control' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Access Control Management</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center mb-3">
                    <Key className="h-5 w-5 text-blue-600 mr-2" />
                    <h4 className="font-medium text-gray-900">User Permissions</h4>
                  </div>
                  <p className="text-sm text-gray-600">Manage user roles and access levels</p>
                  <button className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700">
                    Manage Permissions
                  </button>
                </div>
                
                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center mb-3">
                    <Lock className="h-5 w-5 text-green-600 mr-2" />
                    <h4 className="font-medium text-gray-900">Password Policies</h4>
                  </div>
                  <p className="text-sm text-gray-600">Configure password requirements</p>
                  <button className="mt-3 px-4 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700">
                    Update Policies
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'audit-logs' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Security Audit Logs</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-600">Detailed audit logs will be displayed here</p>
              </div>
            </div>
          )}

          {activeSection === 'settings' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
                    <p className="text-sm text-gray-600">Require 2FA for all admin accounts</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                  </label>
                </div>
                
                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Session Timeout</h4>
                    <p className="text-sm text-gray-600">Automatically log out inactive users</p>
                  </div>
                  <select className="px-3 py-2 border border-gray-300 rounded-lg">
                    <option>30 minutes</option>
                    <option>1 hour</option>
                    <option>2 hours</option>
                    <option>4 hours</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SecurityPage;
