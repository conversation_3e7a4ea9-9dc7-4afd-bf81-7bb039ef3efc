import { useState, useEffect } from 'react';
import { X, Star, ShoppingCart, Package, Truck, Shield, Heart, Share2, ZoomIn, ChevronLeft, ChevronRight } from 'lucide-react';
import { Product } from '../../types/inventory';
import { getProductById } from '../../services/inventoryService';
import { realTimeService } from '../../services/realTimeService';

interface ProductDetailsModalProps {
  productId: string | null;
  isOpen: boolean;
  onClose: () => void;
  userType: 'client' | 'reseller';
  onAddToCart?: (product: any) => void;
}

const ProductDetailsModal = ({ productId, isOpen, onClose, userType, onAddToCart }: ProductDetailsModalProps) => {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);

  useEffect(() => {
    if (isOpen && productId) {
      loadProduct();
    }
  }, [isOpen, productId]);

  useEffect(() => {
    if (!isOpen) return;

    // Subscribe to real-time product updates
    const unsubscribe = realTimeService.subscribe('product-updated', (event) => {
      if (event.data.productId === productId || event.data.id === productId) {
        setProduct(event.data.newData || event.data);
      }
    });

    const unsubscribeImage = realTimeService.subscribe('image-updated', (event) => {
      if (event.data.productId === productId) {
        setProduct(prev => prev ? {
          ...prev,
          featuredImage: event.data.featuredImage,
          thumbnailImages: event.data.thumbnailImages
        } : null);
      }
    });

    return () => {
      unsubscribe();
      unsubscribeImage();
    };
  }, [isOpen, productId]);

  const loadProduct = async () => {
    if (!productId) return;
    
    try {
      setLoading(true);
      setError(null);
      const productData = await getProductById(productId);
      if (productData) {
        setProduct(productData);
        setSelectedImageIndex(0);
      } else {
        setError('Product not found');
      }
    } catch (err) {
      setError('Failed to load product details');
      console.error('Error loading product:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    if (product && onAddToCart) {
      const cartProduct = {
        id: parseInt(product.id.replace('PRD-', '')),
        title: product.title,
        category: product.category,
        price: product.price,
        resellerPrice: product.resellerPrice,
        image: product.featuredImage,
        rating: product.rating,
        inStock: product.stock > 0,
        stock: product.stock
      };
      
      for (let i = 0; i < quantity; i++) {
        onAddToCart(cartProduct);
      }
      
      // Show success feedback
      console.log(`Added ${quantity} ${product.title} to cart`);
    }
  };

  const handleImageSelect = (index: number) => {
    setSelectedImageIndex(index);
  };

  const handlePrevImage = () => {
    if (product) {
      const totalImages = [product.featuredImage, ...product.thumbnailImages].length;
      setSelectedImageIndex((prev) => (prev - 1 + totalImages) % totalImages);
    }
  };

  const handleNextImage = () => {
    if (product) {
      const totalImages = [product.featuredImage, ...product.thumbnailImages].length;
      setSelectedImageIndex((prev) => (prev + 1) % totalImages);
    }
  };

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} Dh`;
  };

  if (!isOpen) return null;

  const allImages = product ? [product.featuredImage, ...product.thumbnailImages] : [];
  const displayPrice = product && userType === 'reseller' ? product.resellerPrice : product?.price || 0;
  const savings = product && userType === 'reseller' ? product.price - product.resellerPrice : 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Product Details</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        {loading && (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading product details...</p>
          </div>
        )}

        {error && (
          <div className="p-8 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={loadProduct}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {product && !loading && !error && (
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Image Gallery */}
              <div className="space-y-4">
                {/* Main Image */}
                <div className="relative bg-gray-100 rounded-xl overflow-hidden aspect-square">
                  <img
                    src={allImages[selectedImageIndex] || '/placeholder.svg'}
                    alt={product.title}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Navigation Arrows */}
                  {allImages.length > 1 && (
                    <>
                      <button
                        onClick={handlePrevImage}
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 transition-all"
                      >
                        <ChevronLeft className="h-5 w-5 text-gray-700" />
                      </button>
                      <button
                        onClick={handleNextImage}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 transition-all"
                      >
                        <ChevronRight className="h-5 w-5 text-gray-700" />
                      </button>
                    </>
                  )}

                  {/* Zoom Icon */}
                  <div className="absolute top-2 right-2 bg-white bg-opacity-80 rounded-full p-2">
                    <ZoomIn className="h-4 w-4 text-gray-700" />
                  </div>
                </div>

                {/* Thumbnail Images */}
                {allImages.length > 1 && (
                  <div className="flex space-x-2 overflow-x-auto">
                    {allImages.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => handleImageSelect(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                          selectedImageIndex === index
                            ? 'border-orange-500 ring-2 ring-orange-200'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <img
                          src={image || '/placeholder.svg'}
                          alt={`${product.title} ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Information */}
              <div className="space-y-6">
                {/* Title and Rating */}
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.title}</h1>
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-5 w-5 ${
                            i < Math.floor(product.rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-gray-600">({product.rating})</span>
                  </div>
                  <p className="text-gray-600">{product.description}</p>
                </div>

                {/* Price */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-3xl font-bold text-gray-900">
                      {formatPrice(displayPrice)}
                    </span>
                    {userType === 'reseller' && savings > 0 && (
                      <span className="text-lg text-gray-500 line-through">
                        {formatPrice(product.price)}
                      </span>
                    )}
                  </div>
                  
                  {userType === 'reseller' && savings > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium">
                        Save {formatPrice(savings)}
                      </span>
                      <span className="text-sm text-gray-600">Reseller Price</span>
                    </div>
                  )}
                </div>

                {/* Stock Status */}
                <div className="flex items-center space-x-2">
                  <Package className="h-5 w-5 text-gray-500" />
                  <span className={`font-medium ${product.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {product.stock > 0 ? `${product.stock} in stock` : 'Out of stock'}
                  </span>
                </div>

                {/* Quantity and Add to Cart */}
                {product.stock > 0 && (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <label className="text-sm font-medium text-gray-700">Quantity:</label>
                      <div className="flex items-center border border-gray-300 rounded-lg">
                        <button
                          onClick={() => setQuantity(Math.max(1, quantity - 1))}
                          className="px-3 py-2 hover:bg-gray-100 transition-colors"
                        >
                          -
                        </button>
                        <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                        <button
                          onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}
                          className="px-3 py-2 hover:bg-gray-100 transition-colors"
                        >
                          +
                        </button>
                      </div>
                    </div>

                    <div className="flex space-x-3">
                      <button
                        onClick={handleAddToCart}
                        className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all font-semibold flex items-center justify-center space-x-2"
                      >
                        <ShoppingCart className="h-5 w-5" />
                        <span>Add to Cart</span>
                      </button>
                      
                      <button
                        onClick={() => setIsWishlisted(!isWishlisted)}
                        className={`px-4 py-3 rounded-lg border transition-all ${
                          isWishlisted
                            ? 'bg-red-50 border-red-200 text-red-600'
                            : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current' : ''}`} />
                      </button>
                      
                      <button className="px-4 py-3 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-100 transition-all">
                        <Share2 className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetailsModal;
