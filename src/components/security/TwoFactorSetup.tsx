
import { useState, useEffect } from 'react';
import { Shield, Smartphone, Mail, Key, Check, X } from 'lucide-react';
import { get2FAStatus, enable2FA, disable2FA } from '../../services/securityService';
import { TwoFactorAuth } from '../../types/security';

const TwoFactorSetup = () => {
  const [twoFactorStatus, setTwoFactorStatus] = useState<TwoFactorAuth | null>(null);
  const [loading, setLoading] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);

  useEffect(() => {
    loadTwoFactorStatus();
  }, []);

  const loadTwoFactorStatus = async () => {
    try {
      const status = await get2FAStatus('current-user-id');
      setTwoFactorStatus(status);
    } catch (error) {
      console.error('Error loading 2FA status:', error);
    }
  };

  const handleEnable2FA = async (method: 'sms' | 'email' | 'authenticator') => {
    setLoading(true);
    try {
      const result = await enable2FA('current-user-id', method);
      setTwoFactorStatus(result);
      setShowBackupCodes(true);
    } catch (error) {
      console.error('Error enabling 2FA:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDisable2FA = async () => {
    setLoading(true);
    try {
      await disable2FA('current-user-id');
      setTwoFactorStatus(null);
    } catch (error) {
      console.error('Error disabling 2FA:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Status Card */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${twoFactorStatus?.isEnabled ? 'bg-green-100 dark:bg-green-900' : 'bg-gray-100 dark:bg-gray-700'}`}>
              <Shield className={`h-6 w-6 ${twoFactorStatus?.isEnabled ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}`} />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Two-Factor Authentication
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {twoFactorStatus?.isEnabled 
                  ? `Enabled via ${twoFactorStatus.method}` 
                  : 'Add an extra layer of security to your account'
                }
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {twoFactorStatus?.isEnabled ? (
              <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
            ) : (
              <X className="h-5 w-5 text-red-600 dark:text-red-400" />
            )}
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              twoFactorStatus?.isEnabled 
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' 
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {twoFactorStatus?.isEnabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>
      </div>

      {/* Setup Options */}
      {!twoFactorStatus?.isEnabled && (
        <div className="grid md:grid-cols-3 gap-6">
          {/* SMS */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Smartphone className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">SMS</h4>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              Receive verification codes via text message
            </p>
            <button
              onClick={() => handleEnable2FA('sms')}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'Setting up...' : 'Enable SMS 2FA'}
            </button>
          </div>

          {/* Email */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <Mail className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Email</h4>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              Receive verification codes via email
            </p>
            <button
              onClick={() => handleEnable2FA('email')}
              disabled={loading}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'Setting up...' : 'Enable Email 2FA'}
            </button>
          </div>

          {/* Authenticator */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <Key className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Authenticator</h4>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              Use an authenticator app like Google Authenticator
            </p>
            <button
              onClick={() => handleEnable2FA('authenticator')}
              disabled={loading}
              className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'Setting up...' : 'Enable Authenticator 2FA'}
            </button>
          </div>
        </div>
      )}

      {/* Backup Codes */}
      {showBackupCodes && twoFactorStatus?.backupCodes && (
        <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-xl p-6">
          <h4 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-3">
            Backup Codes
          </h4>
          <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-4">
            Save these backup codes in a safe place. You can use them to access your account if you lose your 2FA device.
          </p>
          <div className="grid grid-cols-2 gap-2 font-mono text-sm">
            {twoFactorStatus.backupCodes.map((code, index) => (
              <div key={index} className="bg-yellow-100 dark:bg-yellow-800 p-2 rounded text-center">
                {code}
              </div>
            ))}
          </div>
          <button
            onClick={() => setShowBackupCodes(false)}
            className="mt-4 bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors"
          >
            I've saved these codes
          </button>
        </div>
      )}

      {/* Disable 2FA */}
      {twoFactorStatus?.isEnabled && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Disable Two-Factor Authentication
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            This will remove the extra security layer from your account. Only disable if necessary.
          </p>
          <button
            onClick={handleDisable2FA}
            disabled={loading}
            className="bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Disabling...' : 'Disable 2FA'}
          </button>
        </div>
      )}
    </div>
  );
};

export default TwoFactorSetup;
