
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '../types/user';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (userData: User) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  sessionExpiry: Date | null;
  refreshSession: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const ROLE_PERMISSIONS = {
  admin: ['*'], // Admin has all permissions
  manager: [
    'inventory.read', 'inventory.write', 'inventory.delete',
    'orders.read', 'orders.write', 'orders.update',
    'users.read', 'users.write',
    'branches.read', 'branches.write',
    'analytics.read', 'reports.read'
  ],
  delivery: [
    'orders.read', 'orders.update',
    'deliveries.read', 'deliveries.write'
  ],
  client: [
    'products.read', 'orders.create', 'orders.read',
    'profile.read', 'profile.write'
  ],
  reseller: [
    'products.read', 'orders.create', 'orders.read', 'orders.bulk',
    'profile.read', 'profile.write', 'wholesale.read'
  ]
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);

  useEffect(() => {
    // Check for stored session on app load
    const storedUser = localStorage.getItem('yala_user');
    const storedExpiry = localStorage.getItem('yala_session_expiry');
    
    if (storedUser && storedExpiry) {
      const expiryDate = new Date(storedExpiry);
      if (expiryDate > new Date()) {
        setUser(JSON.parse(storedUser));
        setSessionExpiry(expiryDate);
      } else {
        // Session expired, clear storage
        localStorage.removeItem('yala_user');
        localStorage.removeItem('yala_session_expiry');
      }
    }
  }, []);

  const login = (userData: User) => {
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 8); // 8 hour session
    
    setUser(userData);
    setSessionExpiry(expiry);
    
    localStorage.setItem('yala_user', JSON.stringify(userData));
    localStorage.setItem('yala_session_expiry', expiry.toISOString());
  };

  const logout = () => {
    setUser(null);
    setSessionExpiry(null);
    localStorage.removeItem('yala_user');
    localStorage.removeItem('yala_session_expiry');
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('yala_user', JSON.stringify(updatedUser));
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    const userPermissions = ROLE_PERMISSIONS[user.userType] || [];
    return userPermissions.includes('*') || userPermissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    return user?.userType === role;
  };

  const refreshSession = () => {
    if (user) {
      const expiry = new Date();
      expiry.setHours(expiry.getHours() + 8);
      setSessionExpiry(expiry);
      localStorage.setItem('yala_session_expiry', expiry.toISOString());
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      isAuthenticated: !!user,
      login,
      logout,
      updateUser,
      hasPermission,
      hasRole,
      sessionExpiry,
      refreshSession
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
