
import { useState, useEffect } from 'react';
import { Building2, ShoppingCart, Package, Users, BarChart3, Truck, Download, Smartphone, Globe, Shield, Sparkles, Zap, Star } from 'lucide-react';
import AuthModal from '../components/auth/AuthModal';
import Dashboard from '../components/Dashboard';

const Index = () => {
  const [user, setUser] = useState<any>(null);
  const [showAuthModal, setShowAuthModal] = useState(false);

  const handleLogin = (userData: any) => {
    setUser(userData);
    setShowAuthModal(false);
    localStorage.setItem('yala_user', JSON.stringify(userData));
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('yala_user');
  };

  // Check for existing user session
  useEffect(() => {
    const savedUser = localStorage.getItem('yala_user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
  }, []);

  if (user) {
    return <Dashboard user={user} onLogout={handleLogout} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-teal-100 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-teal-600 to-teal-700 p-2 rounded-xl shadow-lg">
                <Building2 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-teal-700 bg-clip-text text-transparent">YalaOffice</h1>
                <p className="text-sm text-gray-600">Smart Supply Management</p>
              </div>
            </div>
            <button
              onClick={() => setShowAuthModal(true)}
              className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-6 py-2.5 rounded-xl hover:from-teal-700 hover:to-teal-800 transition-all duration-300 font-medium shadow-lg hover:shadow-xl hover:scale-105"
            >
              Sign In
            </button>
          </div>
        </div>
      </header>

      {/* Installation Banner */}
      <div className="bg-gradient-to-r from-orange-500 via-orange-600 to-red-500 text-white py-8 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="flex items-center justify-between flex-wrap gap-6">
            <div className="flex items-center space-x-6">
              <div className="bg-white/20 backdrop-blur-sm p-4 rounded-2xl shadow-lg">
                <Download className="h-10 w-10 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold mb-2 flex items-center gap-2">
                  <Sparkles className="h-6 w-6" />
                  Install YalaOffice
                </h3>
                <p className="text-orange-100 mb-2 text-lg">Add to your home screen for lightning-fast access and offline capabilities</p>
                <p className="text-sm text-orange-200 flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Look for "Add to Home Screen" in your browser menu
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4 bg-white/10 backdrop-blur-sm px-6 py-3 rounded-2xl">
              <Smartphone className="h-8 w-8 text-orange-200" />
              <div>
                <p className="text-white font-semibold">Mobile Ready</p>
                <p className="text-orange-200 text-sm">Works anywhere</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 bg-teal-50 text-teal-700 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <Star className="h-4 w-4" />
            Trusted by 500+ businesses
          </div>
          <h2 className="text-5xl md:text-7xl font-extrabold text-gray-900 mb-8 leading-tight">
            Transform Your
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-600 via-orange-500 to-red-500"> Supply Chain </span>
            Today
          </h2>
          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto mb-12 leading-relaxed">
            The most advanced office and school supply management platform. 
            Multi-branch operations, real-time tracking, and intelligent analytics - all in one powerful solution.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <button
              onClick={() => setShowAuthModal(true)}
              className="bg-gradient-to-r from-teal-600 to-orange-500 text-white px-10 py-5 rounded-2xl hover:from-teal-700 hover:to-orange-600 transition-all duration-300 font-bold text-xl shadow-2xl hover:shadow-3xl hover:scale-105"
            >
              Start Free Trial
            </button>
            <button className="border-2 border-gray-300 text-gray-700 px-10 py-5 rounded-2xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 font-semibold text-xl">
              Watch Demo
            </button>
          </div>
        </div>

        {/* Key Benefits */}
        <div className="grid md:grid-cols-3 gap-10 mb-24">
          <div className="text-center group hover:scale-105 transition-transform duration-300">
            <div className="bg-gradient-to-br from-teal-100 to-teal-200 p-6 rounded-3xl w-20 h-20 mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
              <Globe className="h-10 w-10 text-teal-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Cross-Platform</h3>
            <p className="text-gray-600 text-lg">Works seamlessly on desktop, tablet, and mobile devices with offline sync</p>
          </div>
          <div className="text-center group hover:scale-105 transition-transform duration-300">
            <div className="bg-gradient-to-br from-orange-100 to-orange-200 p-6 rounded-3xl w-20 h-20 mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
              <Shield className="h-10 w-10 text-orange-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Enterprise Security</h3>
            <p className="text-gray-600 text-lg">Bank-grade security with role-based access and audit trails</p>
          </div>
          <div className="text-center group hover:scale-105 transition-transform duration-300">
            <div className="bg-gradient-to-br from-red-100 to-red-200 p-6 rounded-3xl w-20 h-20 mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
              <Building2 className="h-10 w-10 text-red-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Multi-Branch</h3>
            <p className="text-gray-600 text-lg">Centralized control with location-specific customization</p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10 mb-24">
          <div className="bg-white p-10 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div className="bg-gradient-to-r from-teal-100 to-teal-200 p-4 rounded-2xl w-fit mb-8">
              <Package className="h-10 w-10 text-teal-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Smart Inventory</h3>
            <p className="text-gray-600 text-lg">
              AI-powered stock management with predictive alerts, automated reordering, and comprehensive categorization.
            </p>
          </div>

          <div className="bg-white p-10 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-4 rounded-2xl w-fit mb-8">
              <ShoppingCart className="h-10 w-10 text-orange-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Order Management</h3>
            <p className="text-gray-600 text-lg">
              Streamlined workflow with multiple payment methods, real-time tracking, and automated invoicing.
            </p>
          </div>

          <div className="bg-white p-10 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div className="bg-gradient-to-r from-red-100 to-red-200 p-4 rounded-2xl w-fit mb-8">
              <Users className="h-10 w-10 text-red-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Role-Based Access</h3>
            <p className="text-gray-600 text-lg">
              Granular permissions for admins, managers, staff, and clients with personalized dashboards.
            </p>
          </div>

          <div className="bg-white p-10 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div className="bg-gradient-to-r from-teal-100 to-teal-200 p-4 rounded-2xl w-fit mb-8">
              <Truck className="h-10 w-10 text-teal-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Smart Delivery</h3>
            <p className="text-gray-600 text-lg">
              GPS tracking, digital signatures, route optimization, and real-time delivery updates.
            </p>
          </div>

          <div className="bg-white p-10 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-4 rounded-2xl w-fit mb-8">
              <BarChart3 className="h-10 w-10 text-orange-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Advanced Analytics</h3>
            <p className="text-gray-600 text-lg">
              AI-driven insights with predictive analytics, performance metrics, and custom reporting.
            </p>
          </div>

          <div className="bg-white p-10 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div className="bg-gradient-to-r from-red-100 to-red-200 p-4 rounded-2xl w-fit mb-8">
              <Sparkles className="h-10 w-10 text-red-700" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Automation</h3>
            <p className="text-gray-600 text-lg">
              Workflow automation, smart notifications, and AI-powered recommendations to boost efficiency.
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-teal-600 via-orange-500 to-red-500 rounded-3xl p-16 text-center text-white shadow-2xl relative overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative">
            <h3 className="text-4xl md:text-5xl font-bold mb-6">Ready to Scale Your Business?</h3>
            <p className="text-xl md:text-2xl mb-10 opacity-90 max-w-3xl mx-auto">
              Join 500+ businesses already using YalaOffice to revolutionize their supply chain operations.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <button
                onClick={() => setShowAuthModal(true)}
                className="bg-white text-gray-900 px-10 py-5 rounded-2xl hover:bg-gray-100 transition-all duration-300 font-bold text-xl shadow-xl hover:shadow-2xl hover:scale-105"
              >
                Start Your Free Trial
              </button>
              <button className="border-2 border-white text-white px-10 py-5 rounded-2xl hover:bg-white hover:text-gray-900 transition-all duration-300 font-bold text-xl">
                Schedule Demo
              </button>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16 mt-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center space-x-4 mb-6">
            <div className="bg-gradient-to-r from-teal-600 to-teal-700 p-3 rounded-2xl">
              <Building2 className="h-8 w-8 text-white" />
            </div>
            <span className="text-2xl font-bold">YalaOffice</span>
          </div>
          <p className="text-center text-gray-400 text-lg">
            © 2024 YalaOffice. The Future of Supply Chain Management.
          </p>
        </div>
      </footer>

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          onClose={() => setShowAuthModal(false)}
          onLogin={handleLogin}
        />
      )}
    </div>
  );
};

export default Index;
