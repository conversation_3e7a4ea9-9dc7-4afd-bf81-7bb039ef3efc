
import { SalesReport, InventoryAnalytics, CustomerAnalytics, KPIMetrics, ReportPeriod } from '../types/analytics';

// Mock analytics data
const mockSalesData = {
  daily: [
    { date: '2024-06-01', sales: 2450, orders: 12 },
    { date: '2024-06-02', sales: 3200, orders: 18 },
    { date: '2024-06-03', sales: 1890, orders: 9 },
    { date: '2024-06-04', sales: 4100, orders: 22 },
    { date: '2024-06-05', sales: 3750, orders: 19 },
    { date: '2024-06-06', sales: 2980, orders: 15 },
    { date: '2024-06-07', sales: 5200, orders: 28 }
  ]
};

export const getSalesReport = async (period: ReportPeriod = 'weekly'): Promise<SalesReport> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  const totalSales = mockSalesData.daily.reduce((sum, day) => sum + day.sales, 0);
  const totalOrders = mockSalesData.daily.reduce((sum, day) => sum + day.orders, 0);

  return {
    period,
    totalSales,
    totalOrders,
    averageOrderValue: totalSales / totalOrders,
    topProducts: [
      { id: 'PRD-001', name: 'Premium Ballpoint Pen Set', sales: 45, revenue: 2070 },
      { id: 'PRD-002', name: 'A4 Notebook Pack', sales: 32, revenue: 2880 },
      { id: 'PRD-003', name: 'Desk Organizer Set', sales: 28, revenue: 3514 },
      { id: 'PRD-004', name: 'Highlighter Set', sales: 38, revenue: 1140 },
      { id: 'PRD-005', name: 'Sticky Notes Pack', sales: 56, revenue: 672 }
    ],
    dailySales: mockSalesData.daily
  };
};

export const getInventoryAnalytics = async (): Promise<InventoryAnalytics> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  return {
    totalProducts: 456,
    lowStockItems: 12,
    outOfStockItems: 3,
    topSellingProducts: [
      { id: 'PRD-001', name: 'Premium Ballpoint Pen Set', unitsSold: 145, turnoverRate: 8.5 },
      { id: 'PRD-002', name: 'A4 Notebook Pack', unitsSold: 132, turnoverRate: 6.2 },
      { id: 'PRD-003', name: 'Desk Organizer Set', unitsSold: 98, turnoverRate: 5.8 },
      { id: 'PRD-004', name: 'Highlighter Set', unitsSold: 156, turnoverRate: 12.3 },
      { id: 'PRD-005', name: 'Sticky Notes Pack', unitsSold: 234, turnoverRate: 15.7 }
    ],
    categoryPerformance: [
      { category: 'Writing Instruments', revenue: 15420, unitsSold: 456, averagePrice: 33.8 },
      { category: 'Paper & Notebooks', revenue: 12890, unitsSold: 234, averagePrice: 55.1 },
      { category: 'Office & Desk Accessories', revenue: 9780, unitsSold: 123, averagePrice: 79.5 },
      { category: 'Art Supplies', revenue: 8650, unitsSold: 189, averagePrice: 45.8 }
    ],
    stockMovements: [
      { date: '2024-06-01', inbound: 120, outbound: 89 },
      { date: '2024-06-02', inbound: 95, outbound: 156 },
      { date: '2024-06-03', inbound: 78, outbound: 92 },
      { date: '2024-06-04', inbound: 145, outbound: 134 },
      { date: '2024-06-05', inbound: 89, outbound: 167 },
      { date: '2024-06-06', inbound: 156, outbound: 98 },
      { date: '2024-06-07', inbound: 112, outbound: 201 }
    ]
  };
};

export const getCustomerAnalytics = async (): Promise<CustomerAnalytics> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  return {
    totalCustomers: 1247,
    newCustomers: 89,
    repeatCustomers: 456,
    customerLifetimeValue: 285.50,
    topCustomers: [
      { id: 'CUST-001', name: 'Ahmed Mansouri', totalSpent: 2450, orderCount: 18 },
      { id: 'CUST-002', name: 'Fatima El Amrani', totalSpent: 1890, orderCount: 12 },
      { id: 'CUST-003', name: 'Youssef Benali', totalSpent: 1650, orderCount: 9 },
      { id: 'CUST-004', name: 'Aicha Tahiri', totalSpent: 1420, orderCount: 15 },
      { id: 'CUST-005', name: 'Omar Kettani', totalSpent: 1380, orderCount: 11 }
    ],
    customerSegments: [
      { segment: 'VIP Customers', count: 45, averageSpend: 1250 },
      { segment: 'Regular Customers', count: 456, averageSpend: 450 },
      { segment: 'New Customers', count: 89, averageSpend: 180 },
      { segment: 'Resellers', count: 67, averageSpend: 2100 }
    ]
  };
};

export const getKPIMetrics = async (): Promise<KPIMetrics> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  return {
    revenue: {
      current: 23570,
      previous: 19450,
      growth: 21.2
    },
    orders: {
      current: 123,
      previous: 98,
      growth: 25.5
    },
    customers: {
      current: 1247,
      previous: 1189,
      growth: 4.9
    },
    averageOrderValue: {
      current: 191.54,
      previous: 198.47,
      growth: -3.5
    },
    conversionRate: {
      current: 3.2,
      previous: 2.8,
      growth: 14.3
    },
    inventoryTurnover: {
      current: 8.5,
      previous: 7.2,
      growth: 18.1
    }
  };
};

// Best Selling Products for Dashboard
export interface BestSellingProduct {
  id: string;
  title: string;
  image: string;
  featuredImage?: string;
  category: string;
  price: number;
  resellerPrice: number;
  unitsSold: number;
  revenue: number;
  growth: number;
  rating: number;
  inStock: boolean;
  stock: number;
}

export const getBestSellingProducts = async (limit: number = 4): Promise<BestSellingProduct[]> => {
  await new Promise(resolve => setTimeout(resolve, 300));

  // Mock data for best selling products in the last month
  const bestSellingProducts: BestSellingProduct[] = [
    {
      id: 'PRD-001',
      title: 'Premium Ballpoint Pen Set',
      image: '/placeholder.svg',
      category: 'Writing Instruments',
      price: 45.99,
      resellerPrice: 38.50,
      unitsSold: 234,
      revenue: 10881,
      growth: 18.5,
      rating: 4.5,
      inStock: true,
      stock: 150
    },
    {
      id: 'PRD-002',
      title: 'A4 Notebook Pack (5 pieces)',
      image: '/placeholder.svg',
      category: 'Paper & Notebooks',
      price: 89.99,
      resellerPrice: 75.00,
      unitsSold: 189,
      revenue: 16962.75,
      growth: 24.2,
      rating: 4.8,
      inStock: true,
      stock: 8
    },
    {
      id: 'PRD-003',
      title: 'Desk Organizer Set',
      image: '/placeholder.svg',
      category: 'Office & Desk Accessories',
      price: 125.50,
      resellerPrice: 105.00,
      unitsSold: 156,
      revenue: 19578,
      growth: 31.7,
      rating: 4.3,
      inStock: true,
      stock: 75
    },
    {
      id: 'PRD-004',
      title: 'Highlighter Set (12 Colors)',
      image: '/placeholder.svg',
      category: 'Writing Instruments',
      price: 32.25,
      resellerPrice: 25.80,
      unitsSold: 298,
      revenue: 9610.50,
      growth: 15.3,
      rating: 4.7,
      inStock: true,
      stock: 67
    },
    {
      id: 'PRD-005',
      title: 'Sticky Notes Mega Pack',
      image: '/placeholder.svg',
      category: 'Paper & Notebooks',
      price: 28.90,
      resellerPrice: 23.12,
      unitsSold: 267,
      revenue: 7716.30,
      growth: 12.8,
      rating: 4.5,
      inStock: true,
      stock: 134
    },
    {
      id: 'PRD-006',
      title: 'Professional Calculator',
      image: '/placeholder.svg',
      category: 'Office & Desk Accessories',
      price: 78.50,
      resellerPrice: 62.80,
      unitsSold: 145,
      revenue: 11382.50,
      growth: 22.1,
      rating: 4.8,
      inStock: true,
      stock: 89
    }
  ];

  // Sort by units sold and return top products
  return bestSellingProducts
    .sort((a, b) => b.unitsSold - a.unitsSold)
    .slice(0, limit);
};
