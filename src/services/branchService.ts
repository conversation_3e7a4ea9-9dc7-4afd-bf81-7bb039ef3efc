import { Branch, BranchInventory, StockTransfer, BranchPerformance } from '../types/branch';
import { Branch as ManagementBranch } from '../types/management';
import { Product } from '../types/inventory';
import { generateId } from '../utils/inventoryUtils';
import { realTimeService } from './realTimeService';

// Mock data storage
let branches: Branch[] = [
  {
    id: 'BR-001',
    name: 'YalaOffice Downtown',
    code: 'YO-DT',
    address: {
      street: '123 Hassan II Boulevard',
      city: 'Casablanca',
      state: 'Grand Casablanca',
      zipCode: '20000',
      country: 'Morocco'
    },
    phone: '+212 522 123 456',
    email: '<EMAIL>',
    managerId: 'MGR-001',
    managerName: 'Ahmed El Mansouri',
    isActive: true,
    operatingHours: {
      open: '08:00',
      close: '20:00',
      timezone: 'Africa/Casablanca'
    },
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z'
  },
  {
    id: 'BR-002',
    name: 'YalaOffice Rabat',
    code: 'YO-RB',
    address: {
      street: '456 Mohammed V Avenue',
      city: 'Rabat',
      state: 'Rabat-Salé-Kénitra',
      zipCode: '10000',
      country: 'Morocco'
    },
    phone: '+212 537 987 654',
    email: '<EMAIL>',
    managerId: 'MGR-002',
    managerName: 'Fatima Zahra',
    isActive: true,
    operatingHours: {
      open: '09:00',
      close: '19:00',
      timezone: 'Africa/Casablanca'
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];

let branchInventory: BranchInventory[] = [
  {
    branchId: 'BR-001',
    productId: 'PRD-001',
    stock: 150,
    minStock: 20,
    maxStock: 200,
    reservedStock: 5,
    lastRestocked: '2024-06-10T10:00:00Z'
  },
  {
    branchId: 'BR-002',
    productId: 'PRD-001',
    stock: 25,
    minStock: 20,
    maxStock: 100,
    reservedStock: 0,
    lastRestocked: '2024-06-08T10:00:00Z'
  }
];

let stockTransfers: StockTransfer[] = [];

// Branch Management
export const getBranches = async (activeOnly: boolean = true): Promise<Branch[]> => {
  return activeOnly ? branches.filter(b => b.isActive) : branches;
};

export const getBranchById = async (id: string): Promise<Branch | null> => {
  return branches.find(b => b.id === id) || null;
};

// Branch Inventory
export const getBranchInventory = async (branchId: string): Promise<BranchInventory[]> => {
  return branchInventory.filter(bi => bi.branchId === branchId);
};

export const getProductAvailabilityByBranch = async (productId: string): Promise<BranchInventory[]> => {
  return branchInventory.filter(bi => bi.productId === productId);
};

export const updateBranchStock = async (
  branchId: string,
  productId: string,
  newStock: number
): Promise<boolean> => {
  const index = branchInventory.findIndex(
    bi => bi.branchId === branchId && bi.productId === productId
  );
  
  if (index !== -1) {
    branchInventory[index].stock = newStock;
    return true;
  }
  return false;
};

// Stock Transfers
export const createStockTransfer = async (
  transferData: Omit<StockTransfer, 'id' | 'requestedAt'>
): Promise<StockTransfer> => {
  const newTransfer: StockTransfer = {
    ...transferData,
    id: generateId('TRF'),
    requestedAt: new Date().toISOString()
  };
  
  stockTransfers.push(newTransfer);
  return newTransfer;
};

export const getStockTransfers = async (branchId?: string): Promise<StockTransfer[]> => {
  if (branchId) {
    return stockTransfers.filter(
      st => st.fromBranchId === branchId || st.toBranchId === branchId
    );
  }
  return stockTransfers;
};

export const updateTransferStatus = async (
  transferId: string,
  status: StockTransfer['status'],
  approvedBy?: string
): Promise<boolean> => {
  const index = stockTransfers.findIndex(st => st.id === transferId);
  if (index !== -1) {
    stockTransfers[index].status = status;
    if (approvedBy) stockTransfers[index].approvedBy = approvedBy;
    if (status === 'completed') stockTransfers[index].completedAt = new Date().toISOString();
    return true;
  }
  return false;
};

// Branch Performance
export const getBranchPerformance = async (period: string = 'monthly'): Promise<BranchPerformance[]> => {
  // Mock performance data
  return [
    {
      branchId: 'BR-001',
      branchName: 'YalaOffice Downtown',
      period,
      totalSales: 45000,
      totalOrders: 156,
      averageOrderValue: 288.46,
      topProducts: [
        { productId: 'PRD-001', productName: 'Premium Ballpoint Pen Set', unitsSold: 45, revenue: 2070 },
        { productId: 'PRD-002', productName: 'A4 Notebook Pack', unitsSold: 32, revenue: 2880 }
      ],
      customerCount: 234,
      inventoryTurnover: 4.2
    },
    {
      branchId: 'BR-002',
      branchName: 'YalaOffice Rabat',
      period,
      totalSales: 32000,
      totalOrders: 98,
      averageOrderValue: 326.53,
      topProducts: [
        { productId: 'PRD-003', productName: 'Desk Organizer Set', unitsSold: 28, revenue: 3514 },
        { productId: 'PRD-001', productName: 'Premium Ballpoint Pen Set', unitsSold: 22, revenue: 1012 }
      ],
      customerCount: 167,
      inventoryTurnover: 3.8
    }
  ];
};

export const createBranch = async (branchData: any, userId: string): Promise<any> => {
  const newBranch = {
    ...branchData,
    id: generateId('BR'),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: userId,
    updatedBy: userId
  };

  branches.push(newBranch);

  // Emit real-time event
  realTimeService.emit('branch-created', {
    branch: newBranch,
    userId
  });

  console.log('Branch created:', newBranch);
  return newBranch;
};

export const updateBranch = async (id: string, updates: any, userId: string): Promise<any> => {
  const index = branches.findIndex(b => b.id === id);
  if (index === -1) return null;

  const oldBranch = branches[index];
  branches[index] = {
    ...branches[index],
    ...updates,
    updatedAt: new Date().toISOString(),
    updatedBy: userId
  };

  // Emit real-time event
  realTimeService.emit('branch-updated', {
    branchId: id,
    oldData: oldBranch,
    newData: branches[index],
    userId
  });

  console.log('Branch updated:', branches[index]);
  return branches[index];
};

export const deleteBranch = async (id: string, userId: string): Promise<boolean> => {
  const index = branches.findIndex(b => b.id === id);
  if (index === -1) return false;

  const branch = branches[index];

  // Check if it's a main branch (you might want to add this property to your Branch type)
  // For now, we'll just prevent deletion of the first branch
  if (index === 0) {
    throw new Error('Cannot delete the main branch. Please designate another branch as main first.');
  }

  branches.splice(index, 1);

  // Emit real-time event
  realTimeService.emit('branch-deleted', {
    branchId: id,
    branch,
    userId
  });

  console.log('Branch deleted:', id);
  return true;
};

// Search branches
export const searchBranches = async (query: string): Promise<Branch[]> => {
  await new Promise(resolve => setTimeout(resolve, 150));

  const searchTerm = query.toLowerCase();
  return branches.filter(branch =>
    branch.name.toLowerCase().includes(searchTerm) ||
    branch.code.toLowerCase().includes(searchTerm) ||
    branch.address.city.toLowerCase().includes(searchTerm) ||
    branch.managerName.toLowerCase().includes(searchTerm)
  );
};
