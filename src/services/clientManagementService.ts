import { realTimeService } from './realTimeService';

export interface Client {
  id: string;
  email: string;
  fullName: string;
  userType: 'client' | 'reseller';
  isActive: boolean;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  company?: string;
  taxId?: string;
  discountRate?: number; // For resellers
  creditLimit?: number; // For resellers
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  registrationDate: string;
  lastLogin?: string;
  notes?: string;
  avatar?: string;
  preferredPaymentMethod?: string;
  loyaltyPoints?: number;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
}

export interface CreateClientData {
  email: string;
  fullName: string;
  userType: 'client' | 'reseller';
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  company?: string;
  taxId?: string;
  discountRate?: number;
  creditLimit?: number;
  notes?: string;
  preferredPaymentMethod?: string;
}

export interface ClientFilters {
  userType?: 'all' | 'client' | 'reseller';
  status?: 'all' | 'active' | 'inactive' | 'suspended' | 'pending';
  city?: string;
  country?: string;
  registrationDateFrom?: string;
  registrationDateTo?: string;
  totalSpentMin?: number;
  totalSpentMax?: number;
}

// Mock data for clients and resellers
let clients: Client[] = [
  {
    id: 'CLI-001',
    email: '<EMAIL>',
    fullName: 'Ahmed Hassan',
    userType: 'client',
    isActive: true,
    phone: '+212 6 12 34 56 78',
    address: '123 Rue Mohammed V',
    city: 'Casablanca',
    country: 'Morocco',
    totalOrders: 15,
    totalSpent: 12500.00,
    lastOrderDate: '2024-01-14T10:30:00Z',
    registrationDate: '2023-06-15T00:00:00Z',
    lastLogin: '2024-01-15T08:45:00Z',
    loyaltyPoints: 1250,
    preferredPaymentMethod: 'Credit Card',
    status: 'active',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 'CLI-002',
    email: '<EMAIL>',
    fullName: 'Fatima Alami',
    userType: 'client',
    isActive: true,
    phone: '+212 6 23 45 67 89',
    address: '456 Avenue Hassan II',
    city: 'Rabat',
    country: 'Morocco',
    totalOrders: 8,
    totalSpent: 6750.00,
    lastOrderDate: '2024-01-12T14:20:00Z',
    registrationDate: '2023-08-20T00:00:00Z',
    lastLogin: '2024-01-14T16:30:00Z',
    loyaltyPoints: 675,
    preferredPaymentMethod: 'Bank Transfer',
    status: 'active',
    avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 'RES-001',
    email: '<EMAIL>',
    fullName: 'TechStore Maroc',
    userType: 'reseller',
    isActive: true,
    phone: '+212 5 22 12 34 56',
    address: '789 Boulevard Zerktouni',
    city: 'Casablanca',
    country: 'Morocco',
    company: 'TechStore Maroc SARL',
    taxId: 'MA123456789',
    discountRate: 15,
    creditLimit: 50000,
    totalOrders: 45,
    totalSpent: 125000.00,
    lastOrderDate: '2024-01-15T09:15:00Z',
    registrationDate: '2023-03-10T00:00:00Z',
    lastLogin: '2024-01-15T11:20:00Z',
    preferredPaymentMethod: 'Credit Terms',
    status: 'active',
    notes: 'Premium reseller with excellent payment history'
  },
  {
    id: 'RES-002',
    email: '<EMAIL>',
    fullName: 'Office Solutions Plus',
    userType: 'reseller',
    isActive: true,
    phone: '+212 5 37 45 67 89',
    address: '321 Rue Allal Ben Abdellah',
    city: 'Rabat',
    country: 'Morocco',
    company: 'Office Solutions Plus',
    taxId: 'MA987654321',
    discountRate: 12,
    creditLimit: 30000,
    totalOrders: 28,
    totalSpent: 78500.00,
    lastOrderDate: '2024-01-13T15:45:00Z',
    registrationDate: '2023-05-22T00:00:00Z',
    lastLogin: '2024-01-14T09:30:00Z',
    preferredPaymentMethod: 'Bank Transfer',
    status: 'active',
    notes: 'Reliable partner for office supplies'
  },
  {
    id: 'CLI-003',
    email: '<EMAIL>',
    fullName: 'Youssef Benali',
    userType: 'client',
    isActive: false,
    phone: '+212 6 34 56 78 90',
    address: '654 Rue de la Liberté',
    city: 'Marrakech',
    country: 'Morocco',
    totalOrders: 3,
    totalSpent: 1200.00,
    lastOrderDate: '2023-12-05T12:00:00Z',
    registrationDate: '2023-11-01T00:00:00Z',
    lastLogin: '2023-12-20T14:15:00Z',
    loyaltyPoints: 120,
    preferredPaymentMethod: 'Cash on Delivery',
    status: 'inactive',
    notes: 'Account deactivated due to inactivity'
  },
  {
    id: 'RES-003',
    email: '<EMAIL>',
    fullName: 'Digital Hub Morocco',
    userType: 'reseller',
    isActive: true,
    phone: '+212 5 24 78 90 12',
    address: '987 Avenue Mohammed VI',
    city: 'Marrakech',
    country: 'Morocco',
    company: 'Digital Hub Morocco',
    taxId: 'MA456789123',
    discountRate: 18,
    creditLimit: 75000,
    totalOrders: 62,
    totalSpent: 185000.00,
    lastOrderDate: '2024-01-15T16:30:00Z',
    registrationDate: '2023-01-15T00:00:00Z',
    lastLogin: '2024-01-15T17:45:00Z',
    preferredPaymentMethod: 'Credit Terms',
    status: 'active',
    notes: 'Top performing reseller with exclusive territory rights'
  }
];

// Client management functions
export const getClients = async (filters?: ClientFilters): Promise<Client[]> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  let filteredClients = [...clients];
  
  if (filters) {
    if (filters.userType && filters.userType !== 'all') {
      filteredClients = filteredClients.filter(client => client.userType === filters.userType);
    }
    
    if (filters.status && filters.status !== 'all') {
      filteredClients = filteredClients.filter(client => client.status === filters.status);
    }
    
    if (filters.city) {
      filteredClients = filteredClients.filter(client => 
        client.city?.toLowerCase().includes(filters.city!.toLowerCase())
      );
    }
    
    if (filters.country) {
      filteredClients = filteredClients.filter(client => 
        client.country?.toLowerCase().includes(filters.country!.toLowerCase())
      );
    }
    
    if (filters.totalSpentMin !== undefined) {
      filteredClients = filteredClients.filter(client => client.totalSpent >= filters.totalSpentMin!);
    }
    
    if (filters.totalSpentMax !== undefined) {
      filteredClients = filteredClients.filter(client => client.totalSpent <= filters.totalSpentMax!);
    }
  }
  
  return filteredClients;
};

export const getClientById = async (id: string): Promise<Client | null> => {
  await new Promise(resolve => setTimeout(resolve, 100));
  return clients.find(client => client.id === id) || null;
};

export const searchClients = async (query: string): Promise<Client[]> => {
  await new Promise(resolve => setTimeout(resolve, 150));
  
  const searchTerm = query.toLowerCase();
  return clients.filter(client => 
    client.fullName.toLowerCase().includes(searchTerm) ||
    client.email.toLowerCase().includes(searchTerm) ||
    client.company?.toLowerCase().includes(searchTerm) ||
    client.phone?.includes(searchTerm) ||
    client.city?.toLowerCase().includes(searchTerm)
  );
};

export const createClient = async (clientData: CreateClientData, createdBy: string): Promise<Client> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Check if email already exists
  const existingClient = clients.find(client => client.email === clientData.email);
  if (existingClient) {
    throw new Error('Email already exists');
  }
  
  const newClient: Client = {
    id: `${clientData.userType === 'reseller' ? 'RES' : 'CLI'}-${String(clients.length + 1).padStart(3, '0')}`,
    email: clientData.email,
    fullName: clientData.fullName,
    userType: clientData.userType,
    isActive: true,
    phone: clientData.phone,
    address: clientData.address,
    city: clientData.city,
    country: clientData.country || 'Morocco',
    company: clientData.company,
    taxId: clientData.taxId,
    discountRate: clientData.discountRate || 0,
    creditLimit: clientData.creditLimit || 0,
    totalOrders: 0,
    totalSpent: 0,
    registrationDate: new Date().toISOString(),
    loyaltyPoints: 0,
    preferredPaymentMethod: clientData.preferredPaymentMethod || 'Credit Card',
    status: 'active',
    notes: clientData.notes
  };
  
  clients.push(newClient);
  
  // Emit real-time event
  realTimeService.emit('client-created', {
    client: newClient,
    createdBy
  });
  
  console.log('Client created:', newClient);
  return newClient;
};

export const updateClient = async (id: string, updates: Partial<Client>, updatedBy: string): Promise<Client | null> => {
  await new Promise(resolve => setTimeout(resolve, 250));
  
  const index = clients.findIndex(client => client.id === id);
  if (index === -1) return null;
  
  const oldClient = clients[index];
  clients[index] = {
    ...clients[index],
    ...updates
  };
  
  // Emit real-time event
  realTimeService.emit('client-updated', {
    clientId: id,
    oldData: oldClient,
    newData: clients[index],
    updatedBy
  });
  
  console.log('Client updated:', clients[index]);
  return clients[index];
};

export const deleteClient = async (id: string, deletedBy: string): Promise<boolean> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const client = clients.find(c => c.id === id);
  if (!client) return false;
  
  clients = clients.filter(c => c.id !== id);
  
  // Emit real-time event
  realTimeService.emit('client-deleted', {
    clientId: id,
    client,
    deletedBy
  });
  
  console.log('Client deleted:', id);
  return true;
};

export const bulkUpdateClients = async (clientIds: string[], updates: Partial<Client>, updatedBy: string): Promise<Client[]> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  
  const updatedClients: Client[] = [];
  
  clientIds.forEach(id => {
    const index = clients.findIndex(client => client.id === id);
    if (index !== -1) {
      const oldClient = clients[index];
      clients[index] = {
        ...clients[index],
        ...updates
      };
      updatedClients.push(clients[index]);
      
      // Emit real-time event for each client
      realTimeService.emit('client-updated', {
        clientId: id,
        oldData: oldClient,
        newData: clients[index],
        updatedBy
      });
    }
  });
  
  console.log('Bulk update completed:', updatedClients.length, 'clients updated');
  return updatedClients;
};

export const bulkDeleteClients = async (clientIds: string[], deletedBy: string): Promise<number> => {
  await new Promise(resolve => setTimeout(resolve, 350));
  
  let deletedCount = 0;
  
  clientIds.forEach(id => {
    const client = clients.find(c => c.id === id);
    if (client) {
      clients = clients.filter(c => c.id !== id);
      deletedCount++;
      
      // Emit real-time event
      realTimeService.emit('client-deleted', {
        clientId: id,
        client,
        deletedBy
      });
    }
  });
  
  console.log('Bulk delete completed:', deletedCount, 'clients deleted');
  return deletedCount;
};

export const getClientStats = async (): Promise<{
  total: number;
  clients: number;
  resellers: number;
  active: number;
  inactive: number;
  totalRevenue: number;
  averageOrderValue: number;
  topSpenders: Client[];
}> => {
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const totalRevenue = clients.reduce((sum, client) => sum + client.totalSpent, 0);
  const totalOrders = clients.reduce((sum, client) => sum + client.totalOrders, 0);
  const topSpenders = [...clients]
    .sort((a, b) => b.totalSpent - a.totalSpent)
    .slice(0, 5);
  
  return {
    total: clients.length,
    clients: clients.filter(c => c.userType === 'client').length,
    resellers: clients.filter(c => c.userType === 'reseller').length,
    active: clients.filter(c => c.status === 'active').length,
    inactive: clients.filter(c => c.status !== 'active').length,
    totalRevenue,
    averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0,
    topSpenders
  };
};

export default {
  getClients,
  getClientById,
  searchClients,
  createClient,
  updateClient,
  deleteClient,
  bulkUpdateClients,
  bulkDeleteClients,
  getClientStats
};
