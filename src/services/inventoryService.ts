
import { Product, Category, StockMovement, LowStockAlert } from '../types/inventory';
import { generateId } from '../utils/inventoryUtils';
import { realTimeService, syncProductData, syncInventoryData, syncImageData } from './realTimeService';

// Event system for real-time updates
type InventoryEventType = 'product-added' | 'product-updated' | 'product-deleted' | 'stock-updated';
type InventoryEventListener = (eventType: InventoryEventType, data: any) => void;

class InventoryEventManager {
  private listeners: InventoryEventListener[] = [];

  subscribe(listener: InventoryEventListener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  emit(eventType: InventoryEventType, data: any) {
    this.listeners.forEach(listener => listener(eventType, data));
  }
}

export const inventoryEventManager = new InventoryEventManager();

// Enhanced mock data storage with persistence simulation
let products: Product[] = [
  {
    id: 'PRD-001',
    title: 'Premium Ballpoint Pen Set',
    description: 'High-quality ballpoint pens perfect for office and school use',
    category: 'Writing Instruments',
    brand: 'PaperMate',
    price: 45.99,
    resellerPrice: 38.50,
    image: '/placeholder.svg',
    featuredImage: '/placeholder.svg',
    thumbnailImages: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    rating: 4.5,
    stock: 150,
    minStock: 20,
    isActive: true,
    isNew: true,
    sku: 'PM-BP-001',
    tags: ['pen', 'ballpoint', 'office', 'premium'],
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  },
  {
    id: 'PRD-002',
    title: 'A4 Notebook Pack (5 pieces)',
    description: 'Quality lined notebooks for students and professionals',
    category: 'Paper & Notebooks',
    brand: 'Oxford',
    price: 89.99,
    resellerPrice: 75.00,
    image: '/placeholder.svg',
    featuredImage: '/placeholder.svg',
    thumbnailImages: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    rating: 4.8,
    stock: 8,
    minStock: 50,
    isActive: true,
    isNew: false,
    sku: 'OX-NB-001',
    tags: ['notebook', 'paper', 'lined', 'pack'],
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  },
  {
    id: 'PRD-003',
    title: 'Desk Organizer Set',
    description: 'Complete desk organization solution with multiple compartments',
    category: 'Office & Desk Accessories',
    brand: 'IKEA',
    price: 125.50,
    resellerPrice: 105.00,
    image: '/placeholder.svg',
    featuredImage: '/placeholder.svg',
    thumbnailImages: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    rating: 4.3,
    stock: 75,
    minStock: 15,
    isActive: true,
    isNew: true,
    sku: 'IK-DO-001',
    tags: ['organizer', 'desk', 'storage', 'office'],
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  }
];

let categories: Category[] = [
  {
    id: 'CAT-001',
    name: 'Writing Instruments',
    description: 'Pens, pencils, markers, and other writing tools',
    isActive: true,
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  },
  {
    id: 'CAT-002',
    name: 'Paper & Notebooks',
    description: 'All types of paper products and notebooks',
    isActive: true,
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  },
  {
    id: 'CAT-003',
    name: 'Office & Desk Accessories',
    description: 'Desk organizers, accessories, and office supplies',
    isActive: true,
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  }
];

let stockMovements: StockMovement[] = [];

// Enhanced Product Management with real-time updates
export const getProducts = async (filters?: {
  category?: string;
  isActive?: boolean;
  lowStock?: boolean;
}): Promise<Product[]> => {
  let filteredProducts = [...products];

  if (filters?.category) {
    filteredProducts = filteredProducts.filter(p => p.category === filters.category);
  }

  if (filters?.isActive !== undefined) {
    filteredProducts = filteredProducts.filter(p => p.isActive === filters.isActive);
  }

  if (filters?.lowStock) {
    filteredProducts = filteredProducts.filter(p => p.stock <= p.minStock);
  }

  return filteredProducts;
};

export const getProductById = async (id: string): Promise<Product | null> => {
  return products.find(p => p.id === id) || null;
};

export const createProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> => {
  const newProduct: Product = {
    ...productData,
    id: generateId('PRD'),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  products.push(newProduct);
  
  // Emit real-time event
  inventoryEventManager.emit('product-added', newProduct);
  
  console.log('New product created:', newProduct);
  return newProduct;
};

export const updateProduct = async (id: string, updates: Partial<Product>, userId?: string): Promise<Product | null> => {
  const index = products.findIndex(p => p.id === id);
  if (index === -1) return null;

  const oldProduct = products[index];
  const updatedProduct = {
    ...products[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  products[index] = updatedProduct;

  // Emit real-time event
  inventoryEventManager.emit('product-updated', updatedProduct);

  // Sync with real-time service for cross-user synchronization
  syncProductData(id, updatedProduct, userId);

  // If images were updated, sync image data specifically
  if (updates.featuredImage || updates.thumbnailImages) {
    syncImageData(id, {
      featuredImage: updatedProduct.featuredImage,
      thumbnailImages: updatedProduct.thumbnailImages
    }, userId);
  }

  console.log('Product updated:', updatedProduct);
  return updatedProduct;
};

export const deleteProduct = async (id: string): Promise<boolean> => {
  const index = products.findIndex(p => p.id === id);
  if (index === -1) return false;

  const deletedProduct = products[index];
  products.splice(index, 1);
  
  // Emit real-time event
  inventoryEventManager.emit('product-deleted', { id, product: deletedProduct });
  
  console.log('Product deleted:', id);
  return true;
};

// Enhanced Stock Management
export const updateStock = async (
  productId: string, 
  quantity: number, 
  type: 'in' | 'out' | 'adjustment',
  reason: string,
  reference?: string,
  createdBy: string = 'system'
): Promise<boolean> => {
  const product = await getProductById(productId);
  if (!product) return false;

  const movement: StockMovement = {
    id: generateId('STK'),
    productId,
    type,
    quantity,
    reason,
    reference,
    createdBy,
    createdAt: new Date().toISOString()
  };

  stockMovements.push(movement);

  // Update product stock
  const newStock = type === 'out' 
    ? product.stock - quantity 
    : product.stock + quantity;

  const updatedProduct = await updateProduct(productId, { stock: Math.max(0, newStock) });
  
  if (updatedProduct) {
    // Emit stock update event
    inventoryEventManager.emit('stock-updated', { 
      productId, 
      oldStock: product.stock, 
      newStock: updatedProduct.stock,
      movement 
    });
  }

  return true;
};

export const getStockMovements = async (productId?: string): Promise<StockMovement[]> => {
  if (productId) {
    return stockMovements.filter(m => m.productId === productId);
  }
  return stockMovements;
};

// Low Stock Alerts
export const getLowStockAlerts = async (): Promise<LowStockAlert[]> => {
  const lowStockProducts = await getProducts({ lowStock: true });
  
  return lowStockProducts.map(product => ({
    id: `ALERT-${product.id}`,
    productId: product.id,
    productTitle: product.title,
    currentStock: product.stock,
    minStock: product.minStock,
    category: product.category,
    isResolved: false,
    createdAt: new Date().toISOString()
  }));
};

// Category Management
export const getCategories = async (activeOnly: boolean = true): Promise<Category[]> => {
  return activeOnly ? categories.filter(c => c.isActive) : categories;
};

export const createCategory = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category> => {
  const newCategory: Category = {
    ...categoryData,
    id: generateId('CAT'),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  categories.push(newCategory);
  return newCategory;
};

export const updateCategory = async (id: string, updates: Partial<Category>): Promise<Category | null> => {
  const index = categories.findIndex(c => c.id === id);
  if (index === -1) return null;

  categories[index] = {
    ...categories[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  return categories[index];
};

// Bulk operations for better performance
export const bulkUpdateProducts = async (updates: Array<{ id: string, data: Partial<Product> }>): Promise<Product[]> => {
  const updatedProducts: Product[] = [];
  
  for (const update of updates) {
    const product = await updateProduct(update.id, update.data);
    if (product) {
      updatedProducts.push(product);
    }
  }
  
  return updatedProducts;
};
