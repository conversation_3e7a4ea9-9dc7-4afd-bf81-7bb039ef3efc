
import { Notification } from '../types/notification';
import { generateId } from '../utils/inventoryUtils';

// Mock notification storage
let notifications: Notification[] = [
  {
    id: 'notif-001',
    userId: 'user-003',
    type: 'order',
    title: 'Order Confirmed',
    message: 'Your order #ORD-001234-ABC has been confirmed and is being processed.',
    isRead: false,
    priority: 'medium',
    actionUrl: '/orders/ORD-001234-ABC',
    metadata: { orderId: 'ORD-001234-ABC' },
    createdAt: '2024-06-13T09:30:00Z'
  },
  {
    id: 'notif-002',
    userId: 'user-002',
    type: 'stock',
    title: 'Low Stock Alert',
    message: 'Premium Pen Set is running low (5 units remaining)',
    isRead: false,
    priority: 'high',
    metadata: { productId: 'PROD-001', stockLevel: 5 },
    createdAt: '2024-06-13T08:15:00Z'
  }
];

export const getUserNotifications = async (userId: string): Promise<Notification[]> => {
  return notifications
    .filter(n => n.userId === userId)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

export const createNotification = async (notificationData: Omit<Notification, 'id' | 'createdAt'>): Promise<Notification> => {
  const newNotification: Notification = {
    ...notificationData,
    id: generateId('NOTIF'),
    createdAt: new Date().toISOString()
  };

  notifications.unshift(newNotification);
  return newNotification;
};

export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  const index = notifications.findIndex(n => n.id === notificationId);
  if (index === -1) return false;

  notifications[index] = {
    ...notifications[index],
    isRead: true,
    readAt: new Date().toISOString()
  };

  return true;
};

export const markAllNotificationsAsRead = async (userId: string): Promise<boolean> => {
  const now = new Date().toISOString();
  notifications = notifications.map(n => 
    n.userId === userId && !n.isRead
      ? { ...n, isRead: true, readAt: now }
      : n
  );
  return true;
};

export const deleteNotification = async (notificationId: string): Promise<boolean> => {
  const index = notifications.findIndex(n => n.id === notificationId);
  if (index === -1) return false;

  notifications.splice(index, 1);
  return true;
};

// Utility functions for creating specific notification types
export const createOrderNotification = async (userId: string, orderId: string, status: string) => {
  const titles = {
    confirmed: 'Order Confirmed',
    shipped: 'Order Shipped',
    delivered: 'Order Delivered',
    cancelled: 'Order Cancelled'
  };

  const messages = {
    confirmed: `Your order #${orderId} has been confirmed and is being processed.`,
    shipped: `Your order #${orderId} has been shipped and is on the way.`,
    delivered: `Your order #${orderId} has been delivered successfully.`,
    cancelled: `Your order #${orderId} has been cancelled.`
  };

  return createNotification({
    userId,
    type: 'order',
    title: titles[status as keyof typeof titles] || 'Order Update',
    message: messages[status as keyof typeof messages] || `Order #${orderId} status updated.`,
    isRead: false,
    priority: 'medium',
    actionUrl: `/orders/${orderId}`,
    metadata: { orderId }
  });
};

export const createStockAlert = async (userId: string, productTitle: string, stockLevel: number, productId: string) => {
  return createNotification({
    userId,
    type: 'stock',
    title: 'Low Stock Alert',
    message: `${productTitle} is running low (${stockLevel} units remaining)`,
    isRead: false,
    priority: stockLevel < 5 ? 'urgent' : 'high',
    metadata: { productId, stockLevel }
  });
};
