import { Order, OrderStatus, PaymentStatus } from '../types/order';
import { generateOrderId, calculateEstimatedDelivery } from '../utils/orderUtils';
import { addOrderTracking } from './orderTrackingService';
import { getProductById, updateStock } from './inventoryService';

// Event system for real-time order updates
type OrderEventType = 'order-created' | 'order-updated' | 'order-status-changed' | 'payment-updated';
type OrderEventListener = (eventType: OrderEventType, data: any) => void;

class OrderEventManager {
  private listeners: OrderEventListener[] = [];

  subscribe(listener: OrderEventListener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  emit(eventType: OrderEventType, data: any) {
    this.listeners.forEach(listener => listener(eventType, data));
  }
}

export const orderEventManager = new OrderEventManager();

// Enhanced mock order storage with real-time updates
let orders: Order[] = [
  {
    id: 'ORD-001234-ABC',
    customerId: 'user-1',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    items: [
      { id: 1, title: 'Premium Pen Set', category: 'Writing Instruments', price: 45.50, quantity: 2, image: '/placeholder.svg' },
      { id: 2, title: 'Notebook Bundle', category: 'Paper & Notebooks', price: 25.75, quantity: 1, image: '/placeholder.svg' }
    ],
    subtotal: 116.75,
    deliveryFee: 30,
    total: 146.75,
    status: 'confirmed',
    paymentMethod: 'Cash on Delivery',
    paymentStatus: 'pending',
    deliveryAddress: '123 Rue Mohammed V, Casablanca, Morocco',
    selectedBranch: 'Casablanca - Downtown',
    createdAt: '2024-06-12T10:30:00Z',
    updatedAt: '2024-06-12T10:30:00Z',
    estimatedDelivery: '2024-06-15T10:30:00Z'
  }
];

export const createOrder = async (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt' | 'estimatedDelivery'>): Promise<Order> => {
  const orderId = generateOrderId();
  const now = new Date().toISOString();
  
  // Validate products and update stock
  for (const item of orderData.items) {
    const product = await getProductById(item.id.toString());
    if (product) {
      // Update stock when order is created
      await updateStock(
        product.id,
        item.quantity,
        'out',
        `Order ${orderId}`,
        orderId,
        orderData.customerId
      );
      
      // Update item with current product data
      item.title = product.title;
      item.price = product.price;
      item.image = product.image;
      item.category = product.category;
    }
  }
  
  const newOrder: Order = {
    ...orderData,
    id: orderId,
    createdAt: now,
    updatedAt: now,
    estimatedDelivery: calculateEstimatedDelivery(now)
  };

  orders.unshift(newOrder);
  
  // Add initial tracking entry
  await addOrderTracking(
    orderId, 
    'order_placed', 
    orderData.selectedBranch, 
    'System',
    'Order has been placed successfully'
  );

  // Emit real-time event
  orderEventManager.emit('order-created', newOrder);
  
  console.log('New order created:', newOrder);
  return newOrder;
};

export const getOrders = async (customerId?: string): Promise<Order[]> => {
  if (customerId) {
    return orders.filter(order => order.customerId === customerId);
  }
  return orders;
};

export const getOrderById = async (orderId: string): Promise<Order | null> => {
  return orders.find(order => order.id === orderId) || null;
};

export const updateOrderStatus = async (orderId: string, status: OrderStatus): Promise<Order | null> => {
  const orderIndex = orders.findIndex(order => order.id === orderId);
  if (orderIndex === -1) return null;

  const oldStatus = orders[orderIndex].status;
  orders[orderIndex] = {
    ...orders[orderIndex],
    status,
    updatedAt: new Date().toISOString()
  };

  // Add tracking entry for status change - using correct OrderTrackingStatus values
  const trackingStatus = 
    status === 'confirmed' ? 'payment_confirmed' :
    status === 'processing' ? 'preparing' :
    status === 'shipped' ? 'out_for_delivery' :
    status === 'delivered' ? 'delivered' :
    status === 'cancelled' ? 'cancelled' :
    'order_placed'; // default fallback

  await addOrderTracking(
    orderId,
    trackingStatus,
    orders[orderIndex].selectedBranch,
    'System',
    `Order status changed from ${oldStatus} to ${status}`
  );

  // Emit real-time event
  orderEventManager.emit('order-status-changed', { 
    orderId, 
    oldStatus, 
    newStatus: status, 
    order: orders[orderIndex] 
  });

  console.log(`Order ${orderId} status updated: ${oldStatus} -> ${status}`);
  return orders[orderIndex];
};

export const updatePaymentStatus = async (orderId: string, paymentStatus: PaymentStatus): Promise<Order | null> => {
  const orderIndex = orders.findIndex(order => order.id === orderId);
  if (orderIndex === -1) return null;

  const oldPaymentStatus = orders[orderIndex].paymentStatus;
  orders[orderIndex] = {
    ...orders[orderIndex],
    paymentStatus,
    updatedAt: new Date().toISOString()
  };

  // Emit real-time event
  orderEventManager.emit('payment-updated', { 
    orderId, 
    oldPaymentStatus, 
    newPaymentStatus: paymentStatus, 
    order: orders[orderIndex] 
  });

  console.log(`Order ${orderId} payment status updated: ${oldPaymentStatus} -> ${paymentStatus}`);
  return orders[orderIndex];
};

export const updateOrder = async (orderId: string, updates: Partial<Order>): Promise<Order | null> => {
  const orderIndex = orders.findIndex(order => order.id === orderId);
  if (orderIndex === -1) return null;

  orders[orderIndex] = {
    ...orders[orderIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  // Emit real-time event
  orderEventManager.emit('order-updated', orders[orderIndex]);
  
  console.log('Order updated:', orders[orderIndex]);
  return orders[orderIndex];
};

export const cancelOrder = async (orderId: string, reason: string): Promise<Order | null> => {
  const order = await getOrderById(orderId);
  if (!order) return null;

  // Restore stock for cancelled orders
  for (const item of order.items) {
    await updateStock(
      item.id.toString(),
      item.quantity,
      'in',
      `Order ${orderId} cancelled: ${reason}`,
      orderId,
      'System'
    );
  }

  return await updateOrderStatus(orderId, 'cancelled');
};

// Get orders with live product data
export const getOrdersWithLiveData = async (customerId?: string): Promise<Order[]> => {
  const ordersList = await getOrders(customerId);
  
  // Update orders with current product information
  for (const order of ordersList) {
    for (const item of order.items) {
      const currentProduct = await getProductById(item.id.toString());
      if (currentProduct) {
        item.title = currentProduct.title;
        item.image = currentProduct.image;
        item.category = currentProduct.category;
        // Note: We keep the original price from when the order was placed
      }
    }
  }
  
  return ordersList;
};
