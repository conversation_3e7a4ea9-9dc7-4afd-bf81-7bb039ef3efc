
import { TwoFactorAuth, Role, Permission, UserRole, AuditTrail, SecuritySettings } from '../types/security';
import { generateId } from '../utils/inventoryUtils';

// Mock data storage
let twoFactorSettings: TwoFactorAuth[] = [];
let roles: Role[] = [
  {
    id: 'role-admin',
    name: 'Administrator',
    description: 'Full system access',
    permissions: [],
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'role-manager',
    name: 'Manager',
    description: 'Branch management access',
    permissions: [],
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'role-client',
    name: 'Client',
    description: 'Customer access',
    permissions: [],
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];
let userRoles: UserRole[] = [];
let auditTrails: AuditTrail[] = [];

// Two-Factor Authentication
export const enable2FA = async (userId: string, method: 'sms' | 'email' | 'authenticator'): Promise<TwoFactorAuth> => {
  const existing = twoFactorSettings.find(tfa => tfa.userId === userId);
  
  if (existing) {
    existing.isEnabled = true;
    existing.method = method;
    return existing;
  }

  const newTFA: TwoFactorAuth = {
    id: generateId('TFA'),
    userId,
    isEnabled: true,
    method,
    backupCodes: Array.from({ length: 10 }, () => 
      Math.random().toString(36).substring(2, 15)
    ),
    createdAt: new Date().toISOString()
  };

  twoFactorSettings.push(newTFA);
  
  // Log the action
  await logAuditTrail({
    userId,
    action: 'enable_2fa',
    resource: 'authentication',
    resourceId: userId,
    details: { method },
    category: 'authentication',
    severity: 'medium'
  });

  return newTFA;
};

export const disable2FA = async (userId: string): Promise<boolean> => {
  const index = twoFactorSettings.findIndex(tfa => tfa.userId === userId);
  if (index === -1) return false;

  twoFactorSettings[index].isEnabled = false;

  await logAuditTrail({
    userId,
    action: 'disable_2fa',
    resource: 'authentication',
    resourceId: userId,
    details: {},
    category: 'authentication',
    severity: 'medium'
  });

  return true;
};

export const get2FAStatus = async (userId: string): Promise<TwoFactorAuth | null> => {
  return twoFactorSettings.find(tfa => tfa.userId === userId) || null;
};

// Role-Based Access Control
export const getAllRoles = async (): Promise<Role[]> => {
  return roles;
};

export const createRole = async (roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> => {
  const newRole: Role = {
    ...roleData,
    id: generateId('ROLE'),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  roles.push(newRole);
  return newRole;
};

export const assignUserRole = async (userId: string, roleId: string, assignedBy: string): Promise<UserRole> => {
  // Remove existing role assignments for this user
  userRoles = userRoles.filter(ur => ur.userId !== userId);

  const newUserRole: UserRole = {
    id: generateId('UR'),
    userId,
    roleId,
    assignedBy,
    assignedAt: new Date().toISOString()
  };

  userRoles.push(newUserRole);

  await logAuditTrail({
    userId: assignedBy,
    action: 'assign_role',
    resource: 'user_roles',
    resourceId: userId,
    details: { roleId },
    category: 'authorization',
    severity: 'medium'
  });

  return newUserRole;
};

export const getUserRoles = async (userId: string): Promise<Role[]> => {
  const userRoleIds = userRoles
    .filter(ur => ur.userId === userId)
    .map(ur => ur.roleId);

  return roles.filter(role => userRoleIds.includes(role.id));
};

// Audit Trail
export const logAuditTrail = async (auditData: Omit<AuditTrail, 'id' | 'userName' | 'ipAddress' | 'userAgent' | 'timestamp'>): Promise<AuditTrail> => {
  const newAudit: AuditTrail = {
    ...auditData,
    id: generateId('AUDIT'),
    userName: 'Current User', // In real app, fetch from user context
    ipAddress: '127.0.0.1', // In real app, get from request
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  };

  auditTrails.push(newAudit);
  return newAudit;
};

export const getAuditTrails = async (filters?: {
  userId?: string;
  action?: string;
  resource?: string;
  severity?: string;
  startDate?: string;
  endDate?: string;
}): Promise<AuditTrail[]> => {
  let filtered = [...auditTrails];

  if (filters) {
    if (filters.userId) {
      filtered = filtered.filter(audit => audit.userId === filters.userId);
    }
    if (filters.action) {
      filtered = filtered.filter(audit => audit.action.includes(filters.action));
    }
    if (filters.resource) {
      filtered = filtered.filter(audit => audit.resource === filters.resource);
    }
    if (filters.severity) {
      filtered = filtered.filter(audit => audit.severity === filters.severity);
    }
    if (filters.startDate) {
      filtered = filtered.filter(audit => audit.timestamp >= filters.startDate!);
    }
    if (filters.endDate) {
      filtered = filtered.filter(audit => audit.timestamp <= filters.endDate!);
    }
  }

  return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

// Security Settings
export const getSecuritySettings = async (): Promise<SecuritySettings> => {
  return {
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      maxAge: 90
    },
    sessionSettings: {
      timeout: 30,
      maxConcurrentSessions: 3,
      requireReauth: true
    },
    twoFactorRequired: false,
    loginAttempts: {
      maxAttempts: 5,
      lockoutDuration: 15
    }
  };
};

export const updateSecuritySettings = async (settings: SecuritySettings): Promise<SecuritySettings> => {
  // In real implementation, save to database
  return settings;
};
