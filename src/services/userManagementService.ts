import { realTimeService } from './realTimeService';

export interface User {
  id: string;
  email: string;
  fullName: string;
  userType: 'admin' | 'manager';
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  permissions: string[];
  avatar?: string;
  phone?: string;
  department?: string;
  branch?: string;
}

export interface CreateUserData {
  email: string;
  fullName: string;
  userType: 'admin' | 'manager';
  password: string;
  phone?: string;
  department?: string;
  branch?: string;
  permissions?: string[];
}

// Mock data for users
let users: User[] = [
  {
    id: 'USR-001',
    email: '<EMAIL>',
    fullName: 'System Administrator',
    userType: 'admin',
    isActive: true,
    lastLogin: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    createdBy: 'system',
    permissions: ['all'],
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    phone: '+212 522 123 456',
    department: 'IT Administration'
  },
  {
    id: 'USR-002',
    email: '<EMAIL>',
    fullName: 'Ahmed Benali',
    userType: 'manager',
    isActive: true,
    lastLogin: '2024-01-15T09:15:00Z',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-15T09:15:00Z',
    createdBy: 'USR-001',
    permissions: ['inventory', 'orders', 'products', 'categories', 'branches'],
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    phone: '+212 522 234 567',
    department: 'Store Management',
    branch: 'Casablanca Central'
  },
  {
    id: 'USR-003',
    email: '<EMAIL>',
    fullName: 'Fatima Alaoui',
    userType: 'manager',
    isActive: true,
    lastLogin: '2024-01-14T16:45:00Z',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-14T16:45:00Z',
    createdBy: 'USR-001',
    permissions: ['inventory', 'orders', 'products'],
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    phone: '+212 537 345 678',
    department: 'Store Management',
    branch: 'Rabat'
  },
  {
    id: 'USR-004',
    email: '<EMAIL>',
    fullName: 'Youssef Tazi',
    userType: 'admin',
    isActive: false,
    lastLogin: '2024-01-10T14:20:00Z',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-10T14:20:00Z',
    createdBy: 'USR-001',
    permissions: ['users', 'analytics', 'reports'],
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    phone: '+212 524 456 789',
    department: 'Business Analytics'
  }
];

// User management functions
export const getUsers = async (): Promise<User[]> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  return [...users];
};

export const getUserById = async (id: string): Promise<User | null> => {
  await new Promise(resolve => setTimeout(resolve, 100));
  return users.find(user => user.id === id) || null;
};

export const getAdminUsers = async (): Promise<User[]> => {
  await new Promise(resolve => setTimeout(resolve, 150));
  return users.filter(user => user.userType === 'admin');
};

export const getManagerUsers = async (): Promise<User[]> => {
  await new Promise(resolve => setTimeout(resolve, 150));
  return users.filter(user => user.userType === 'manager');
};

export const createUser = async (userData: CreateUserData, createdBy: string): Promise<User> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Check if email already exists
  const existingUser = users.find(user => user.email === userData.email);
  if (existingUser) {
    throw new Error('Email already exists');
  }
  
  const newUser: User = {
    id: `USR-${String(users.length + 1).padStart(3, '0')}`,
    email: userData.email,
    fullName: userData.fullName,
    userType: userData.userType,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy,
    permissions: userData.permissions || (userData.userType === 'admin' ? ['all'] : ['inventory', 'orders']),
    phone: userData.phone,
    department: userData.department,
    branch: userData.branch
  };
  
  users.push(newUser);
  
  // Emit real-time event
  realTimeService.emit('user-created', {
    user: newUser,
    createdBy
  });
  
  console.log('User created:', newUser);
  return newUser;
};

export const updateUser = async (id: string, updates: Partial<User>, updatedBy: string): Promise<User | null> => {
  await new Promise(resolve => setTimeout(resolve, 250));
  
  const index = users.findIndex(user => user.id === id);
  if (index === -1) return null;
  
  const oldUser = users[index];
  users[index] = {
    ...users[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  // Emit real-time event
  realTimeService.emit('user-updated', {
    userId: id,
    oldData: oldUser,
    newData: users[index],
    updatedBy
  });
  
  console.log('User updated:', users[index]);
  return users[index];
};

export const deleteUser = async (id: string, deletedBy: string): Promise<boolean> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const user = users.find(u => u.id === id);
  if (!user) return false;
  
  // Prevent deletion of the last admin
  const adminUsers = users.filter(u => u.userType === 'admin' && u.isActive);
  if (user.userType === 'admin' && adminUsers.length <= 1) {
    throw new Error('Cannot delete the last active admin user');
  }
  
  // Prevent self-deletion
  if (id === deletedBy) {
    throw new Error('Cannot delete your own account');
  }
  
  users = users.filter(u => u.id !== id);
  
  // Emit real-time event
  realTimeService.emit('user-deleted', {
    userId: id,
    user,
    deletedBy
  });
  
  console.log('User deleted:', id);
  return true;
};

export const toggleUserStatus = async (id: string, updatedBy: string): Promise<User | null> => {
  await new Promise(resolve => setTimeout(resolve, 150));
  
  const user = users.find(u => u.id === id);
  if (!user) return null;
  
  // Prevent deactivating the last admin
  if (user.userType === 'admin' && user.isActive) {
    const activeAdmins = users.filter(u => u.userType === 'admin' && u.isActive);
    if (activeAdmins.length <= 1) {
      throw new Error('Cannot deactivate the last active admin user');
    }
  }
  
  user.isActive = !user.isActive;
  user.updatedAt = new Date().toISOString();
  
  // Emit real-time event
  realTimeService.emit('user-status-changed', {
    userId: id,
    isActive: user.isActive,
    updatedBy
  });
  
  console.log('User status toggled:', user);
  return user;
};

export const searchUsers = async (query: string): Promise<User[]> => {
  await new Promise(resolve => setTimeout(resolve, 150));
  
  const searchTerm = query.toLowerCase();
  return users.filter(user => 
    user.fullName.toLowerCase().includes(searchTerm) ||
    user.email.toLowerCase().includes(searchTerm) ||
    user.department?.toLowerCase().includes(searchTerm) ||
    user.branch?.toLowerCase().includes(searchTerm)
  );
};

export const getUserStats = async (): Promise<{
  total: number;
  admins: number;
  managers: number;
  active: number;
  inactive: number;
  recentlyCreated: number;
}> => {
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const now = new Date();
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  
  return {
    total: users.length,
    admins: users.filter(u => u.userType === 'admin').length,
    managers: users.filter(u => u.userType === 'manager').length,
    active: users.filter(u => u.isActive).length,
    inactive: users.filter(u => !u.isActive).length,
    recentlyCreated: users.filter(u => new Date(u.createdAt) > weekAgo).length
  };
};

export default {
  getUsers,
  getUserById,
  getAdminUsers,
  getManagerUsers,
  createUser,
  updateUser,
  deleteUser,
  toggleUserStatus,
  searchUsers,
  getUserStats
};
