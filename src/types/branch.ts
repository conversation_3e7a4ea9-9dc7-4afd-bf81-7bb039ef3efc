
export interface Branch {
  id: string;
  name: string;
  code: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  phone: string;
  email: string;
  managerId: string;
  managerName: string;
  isActive: boolean;
  operatingHours: {
    open: string;
    close: string;
    timezone: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface BranchInventory {
  branchId: string;
  productId: string;
  stock: number;
  minStock: number;
  maxStock: number;
  reservedStock: number;
  lastRestocked: string;
}

export interface StockTransfer {
  id: string;
  fromBranchId: string;
  toBranchId: string;
  productId: string;
  quantity: number;
  status: 'pending' | 'in-transit' | 'completed' | 'cancelled';
  requestedBy: string;
  approvedBy?: string;
  notes?: string;
  requestedAt: string;
  completedAt?: string;
}

export interface BranchPerformance {
  branchId: string;
  branchName: string;
  period: string;
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  topProducts: {
    productId: string;
    productName: string;
    unitsSold: number;
    revenue: number;
  }[];
  customerCount: number;
  inventoryTurnover: number;
}
